{"css.validate": false, "scss.validate": false, "less.validate": false, "tailwindCSS.includeLanguages": {"css": "css", "html": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascriptreact", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "editor.quickSuggestions": {"strings": true}, "editor.autoClosingBrackets": "always", "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "files.associations": {"*.css": "tailwindcss"}}