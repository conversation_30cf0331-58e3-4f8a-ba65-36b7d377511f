{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AdminHeaderProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { setTheme, resolvedTheme } = useTheme()\n  const [isProfileOpen, setIsProfileOpen] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const { user, logout } = useAuth()\n\n  // Handle hydration\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n\n\n  const navigationItems = [\n    {\n      id: 'dashboard',\n      label: 'Home Dashboard',\n      icon: Home,\n      tooltip: 'Dashboard Overview'\n    },\n    {\n      id: 'products',\n      label: 'Product Lists',\n      icon: Package,\n      tooltip: 'Manage Products'\n    },\n    {\n      id: 'debts',\n      label: 'Customer Debts',\n      icon: Users,\n      tooltip: 'Customer Debt Management'\n    },\n    {\n      id: 'family-gallery',\n      label: 'Family Gallery',\n      icon: Image,\n      tooltip: 'Family Photos & Memories'\n    },\n  ]\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Implement search functionality\n    console.log('Searching for:', searchQuery)\n  }\n\n  const toggleTheme = () => {\n    if (!mounted) return\n\n    // Manual DOM manipulation for immediate visual feedback\n    const html = document.documentElement\n    const isDark = resolvedTheme === 'dark'\n\n    if (isDark) {\n      html.classList.remove('dark')\n      setTheme('light')\n    } else {\n      html.classList.add('dark')\n      setTheme('dark')\n    }\n  }\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300\" style={{\n      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'\n    }}>\n      <div className=\"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4\">\n        \n        {/* Left Section - Logo & Search (Fixed Width) */}\n        <div className=\"flex items-center space-x-3 w-auto\">\n          {/* Revantad Logo */}\n          <Link\n            href=\"/landing\"\n            className=\"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0\"\n            title=\"Return to Front Page\"\n          >\n            <div className=\"w-10 h-10 hero-gradient rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">R</span>\n            </div>\n            <span className=\"text-xl font-bold text-gradient hidden sm:block\">Revantad</span>\n          </Link>\n\n          {/* Search Bar - Much Shorter than Sidebar (320px) */}\n          <form onSubmit={handleSearch} className=\"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200\"\n              />\n            </div>\n          </form>\n        </div>\n\n        {/* Center Section - Navigation Icons (Facebook-style) */}\n        <div className=\"hidden sm:flex items-center justify-center\">\n          <div className=\"flex items-center space-x-3 md:space-x-4 lg:space-x-5\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              const isActive = activeSection === item.id\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => setActiveSection(item.id)}\n                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]`}\n                  style={{\n                    backgroundColor: isActive\n                      ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                      : 'transparent',\n                    color: isActive\n                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),\n                    boxShadow: isActive ? '0 2px 8px rgba(34, 197, 94, 0.2)' : 'none'\n                  }}\n                  title={item.tooltip}\n                  onMouseEnter={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(243, 244, 246, 0.8)'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = 'transparent'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'\n                    }\n                  }}\n                >\n                  <Icon className=\"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200\" />\n                  {isActive && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full\"></div>\n                  )}\n\n                  {/* Enhanced Tooltip */}\n                  <div className=\"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10\">\n                    {item.tooltip}\n                    <div className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45\"></div>\n                  </div>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Mobile Navigation - Simplified */}\n        <div className=\"sm:hidden flex items-center justify-center\">\n          <button\n            onClick={() => setActiveSection(activeSection === 'dashboard' ? 'products' : 'dashboard')}\n            className=\"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\"\n            title=\"Toggle View\"\n          >\n            {activeSection === 'dashboard' ? (\n              <Package className=\"h-5 w-5\" />\n            ) : (\n              <Home className=\"h-5 w-5\" />\n            )}\n          </button>\n        </div>\n\n        {/* Right Section - Dark Mode & Profile */}\n        <div className=\"flex items-center justify-end space-x-3\">\n\n\n          {/* Dark Mode Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0\"\n            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}\n            disabled={!mounted}\n          >\n            {!mounted ? (\n              <div className=\"h-5 w-5 bg-gray-400 rounded-full animate-pulse\" />\n            ) : resolvedTheme === 'dark' ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group\"\n            >\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow\">\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors\">\n                {user?.name || 'Admin'}\n              </span>\n            </button>\n\n            {/* Dropdown Menu */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1\">\n                <div className=\"px-4 py-2 border-b border-gray-200 dark:border-slate-700\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user?.name || 'Admin User'}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.email || '<EMAIL>'}</p>\n                </div>\n                \n                <button\n                  onClick={() => setActiveSection('settings')}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>Settings</span>\n                </button>\n                \n                <button\n                  onClick={handleLogout}\n                  className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Logout</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAae,SAAS,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAoB;;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/B,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAIL,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,OAAI;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,iCAAiC;QACjC,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QAEd,wDAAwD;QACxD,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,SAAS,kBAAkB;QAEjC,IAAI,QAAQ;YACV,KAAK,SAAS,CAAC,MAAM,CAAC;YACtB,SAAS;QACX,OAAO;YACL,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;QAAgJ,OAAO;YACvK,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;QACtD;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAM;;8CAEN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAIpE,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;4BAE1C,qBACE,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gCACvC,WAAW,CAAC,yIAAyI,CAAC;gCACtJ,OAAO;oCACL,iBAAiB,WACZ,kBAAkB,SAAS,2BAA2B,2BACvD;oCACJ,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oCAC5C,WAAW,WAAW,qCAAqC;gCAC7D;gCACA,OAAO,KAAK,OAAO;gCACnB,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,2BAA2B;wCAC9F,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;gCACA,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;;kDAEA,6LAAC;wCAAK,WAAU;;;;;;oCACf,0BACC,6LAAC;wCAAI,WAAU;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO;0DACb,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAlCZ,KAAK,EAAE;;;;;wBAsClB;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS,IAAM,iBAAiB,kBAAkB,cAAc,aAAa;wBAC7E,WAAU;wBACV,OAAM;kCAEL,kBAAkB,4BACjB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAMtB,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO,UAAU,CAAC,UAAU,EAAE,kBAAkB,SAAS,UAAU,OAAO,gBAAgB,EAAE,cAAc,CAAC,CAAC,GAAG;4BAC/G,UAAU,CAAC;sCAEV,CAAC,wBACA,6LAAC;gCAAI,WAAU;;;;;uCACb,kBAAkB,uBACpB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;qDAEf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDACb,MAAM,QAAQ;;;;;;;;;;;;gCAKlB,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqD,MAAM,QAAQ;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAA4C,MAAM,SAAS;;;;;;;;;;;;sDAG1E,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA1OwB;;QAEc,mJAAA,CAAA,WAAQ;QAGnB,kIAAA,CAAA,UAAO;;;KALV", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\ninterface SidebarProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {\n  const { resolvedTheme } = useTheme()\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    const savedState = localStorage.getItem('sidebar-collapsed')\n    if (savedState !== null) {\n      setIsCollapsed(JSON.parse(savedState))\n    }\n  }, [])\n\n  useEffect(() => {\n    if (mounted) {\n      localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))\n    }\n  }, [isCollapsed, mounted])\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed)\n  }\n\n  const menuItems = [\n    {\n      id: 'api-graphing',\n      label: 'API Graphing & Visuals',\n      icon: BarChart3\n    },\n    {\n      id: 'history',\n      label: 'History',\n      icon: History\n    },\n    {\n      id: 'calendar',\n      label: 'Calendar',\n      icon: Calendar\n    },\n    {\n      id: 'settings',\n      label: 'Settings',\n      icon: Settings\n    },\n  ]\n\n  return (\n    <div\n      className={`shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out ${\n        isCollapsed ? 'w-20 min-w-20' : 'w-80 min-w-80'\n      }`}\n      style={{\n        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n        borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',\n        borderWidth: '1px',\n        boxShadow: resolvedTheme === 'dark'\n          ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n          : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n      }}\n    >\n      <div className=\"flex flex-col h-full\">\n        <div\n          className={`sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)',\n            borderBottom: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'mb-3'}`}>\n            <div\n              className={`rounded-lg flex items-center justify-center transition-all duration-300 ${\n                isCollapsed ? 'w-10 h-10' : 'w-8 h-8 mr-3'\n              }`}\n              style={{\n                background: resolvedTheme === 'dark'\n                  ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'\n                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                boxShadow: '0 4px 8px rgba(34, 197, 94, 0.3)'\n              }}\n            >\n              <span className={`text-white font-bold ${isCollapsed ? 'text-base' : 'text-sm'}`}>⚡</span>\n            </div>\n            {!isCollapsed && (\n              <h2\n                className=\"text-lg font-bold transition-all duration-300 crisp-text\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                  textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                }}\n              >\n                Additional Tools\n              </h2>\n            )}\n          </div>\n          {!isCollapsed && (\n            <p\n              className=\"text-xs font-medium transition-all duration-300 crisp-text\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b',\n                letterSpacing: '0.025em'\n              }}\n            >\n              Advanced features and utilities\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex-1 relative\">\n          <button\n            onClick={toggleSidebar}\n            className=\"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' \n                ? 'rgba(34, 197, 94, 0.1)' \n                : 'rgba(34, 197, 94, 0.08)',\n              border: resolvedTheme === 'dark' \n                ? '1px solid rgba(34, 197, 94, 0.3)' \n                : '1px solid rgba(34, 197, 94, 0.2)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 2px 8px rgba(34, 197, 94, 0.2)'\n                : '0 2px 8px rgba(34, 197, 94, 0.15)'\n            }}\n            title={isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}\n          >\n            {isCollapsed ? (\n              <ChevronRight \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            ) : (\n              <ChevronLeft \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            )}\n          </button>\n\n          <div className=\"absolute inset-0 scroll-fade-top scroll-fade-bottom\">\n            <nav className={`h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ${\n              isCollapsed ? 'px-2' : 'px-4'\n            }`}>\n        {menuItems.map((item) => {\n          const Icon = item.icon\n          const isActive = activeSection === item.id\n\n          return (\n            <div key={item.id} className=\"relative group\">\n              <button\n                onClick={() => setActiveSection(item.id)}\n                className={`w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ${\n                  isCollapsed\n                    ? 'p-2.5 rounded-lg justify-center'\n                    : 'p-3 rounded-xl'\n                }`}\n                style={{\n                  background: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'\n                      : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')\n                    : 'transparent',\n                  border: isActive\n                    ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')\n                    : '1px solid transparent',\n                  boxShadow: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                      : '0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                    : 'none'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = resolvedTheme === 'dark'\n                      ? 'rgba(71, 85, 105, 0.15)'\n                      : 'rgba(243, 244, 246, 0.6)'\n                    e.currentTarget.style.border = resolvedTheme === 'dark'\n                      ? '1px solid rgba(148, 163, 184, 0.2)'\n                      : '1px solid rgba(229, 231, 235, 0.6)'\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = 'transparent'\n                    e.currentTarget.style.border = '1px solid transparent'\n                  }\n                }}\n              >\n                {isCollapsed ? (\n                  <Icon\n                    className=\"h-5 w-5 transition-all duration-200 relative z-10\"\n                    style={{\n                      color: isActive\n                        ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                        : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                      filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                    }}\n                  />\n                ) : (\n                  <>\n                    <div\n                      className=\"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden\"\n                      style={{\n                        background: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'\n                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')\n                          : (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)'\n                            : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'),\n                        boxShadow: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? '0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                            : '0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                          : (resolvedTheme === 'dark'\n                            ? 'inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                            : 'inset 0 1px 0 rgba(255, 255, 255, 0.9)')\n                      }}\n                    >\n                      <Icon\n                        className=\"h-4 w-4 transition-all duration-200 relative z-10\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                          filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                        }}\n                      />\n                    </div>\n                    <div className=\"flex-1 sidebar-text\">\n                      <h3\n                        className=\"font-medium text-sm transition-colors duration-200 leading-snug\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827'),\n                          fontWeight: isActive ? '600' : '500'\n                        }}\n                      >\n                        {item.label}\n                      </h3>\n                    </div>\n                  </>\n                )}\n              </button>\n              \n              {isCollapsed && (\n                <div\n                  className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',\n                    boxShadow: resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(0, 0, 0, 0.3)'\n                      : '0 4px 12px rgba(0, 0, 0, 0.15)'\n                  }}\n                >\n                  <div className=\"font-semibold\">{item.label}</div>\n                  <div\n                    className=\"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0\"\n                    style={{\n                      borderTop: '6px solid transparent',\n                      borderBottom: '6px solid transparent',\n                      borderRight: `6px solid ${resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'}`\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n          )\n        })}\n            </nav>\n          </div>\n        </div>\n\n        {/* Enhanced Sticky Footer Section */}\n        <div\n          className={`sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)',\n            borderTop: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div\n            className=\"text-sm transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            {isCollapsed ? (\n              <div className=\"flex justify-center\">\n                <div\n                  className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10\"\n                  style={{\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                  }}\n                >\n                  <span className=\"text-white font-bold relative z-10 text-base\">R</span>\n                  <div\n                    className=\"absolute inset-0 opacity-20\"\n                    style={{\n                      background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                    }}\n                  />\n                </div>\n              </div>\n            ) : (\n              <>\n                <div className=\"flex items-center mb-2 space-x-3\">\n                  <div\n                    className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8\"\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                    }}\n                  >\n                    <span className=\"text-white font-bold relative z-10 text-sm\">R</span>\n                    <div\n                      className=\"absolute inset-0 opacity-20\"\n                      style={{\n                        background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                      }}\n                    />\n                  </div>\n                  <div>\n                    <span\n                      className=\"font-bold text-sm transition-colors duration-300 block\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#1e293b',\n                        textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                      }}\n                    >\n                      Revantad Store\n                    </span>\n                    <span\n                      className=\"text-xs font-medium\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',\n                        letterSpacing: '0.025em'\n                      }}\n                    >\n                      Professional Business Management\n                    </span>\n                  </div>\n                </div>\n                <div\n                  className=\"text-xs font-medium px-3 py-2 rounded-lg\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(229, 231, 235, 0.6)'\n                  }}\n                >\n                  Admin Dashboard v2.0\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        {!isCollapsed && (\n          <div\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 opacity-60 pointer-events-none\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            <div className=\"flex flex-col items-center space-y-1\">\n              <div className=\"text-xs font-medium\">Scroll for more</div>\n              <div className=\"flex space-x-1\">\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\"></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAXA;;;;AAkBe,SAAS,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAgB;;IAC/E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,WAAW;YACX,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,eAAe,MAAM;gBACvB,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAC3D;QACF;4BAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qNAAA,CAAA,YAAS;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,8FAA8F,EACxG,cAAc,kBAAkB,iBAChC;QACF,OAAO;YACL,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;YACpD,aAAa;YACb,WAAW,kBAAkB,SACzB,8EACA;QACN;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAW,CAAC,+DAA+D,EACzE,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,cAAc,kBAAkB,SAC5B,uCACA;wBACJ,WAAW,kBAAkB,SACzB,yEACA;oBACN;;sCAEA,6LAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,QAAQ;;8CAC5E,6LAAC;oCACC,WAAW,CAAC,wEAAwE,EAClF,cAAc,cAAc,gBAC5B;oCACF,OAAO;wCACL,YAAY,kBAAkB,SAC1B,sDACA;wCACJ,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAW,CAAC,qBAAqB,EAAE,cAAc,cAAc,WAAW;kDAAE;;;;;;;;;;;gCAEnF,CAAC,6BACA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oCAC1E;8CACD;;;;;;;;;;;;wBAKJ,CAAC,6BACA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,kBAAkB,SAAS,YAAY;gCAC9C,eAAe;4BACjB;sCACD;;;;;;;;;;;;8BAML,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAC/B,2BACA;gCACJ,QAAQ,kBAAkB,SACtB,qCACA;gCACJ,WAAW,kBAAkB,SACzB,qCACA;4BACN;4BACA,OAAO,cAAc,mBAAmB;sCAEvC,4BACC,6LAAC,yNAAA,CAAA,eAAY;gCACX,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;qDAGnE,6LAAC,uNAAA,CAAA,cAAW;gCACV,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;;;;;;;sCAKvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,2FAA2F,EAC1G,cAAc,SAAS,QACvB;0CACL,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;oCAE1C,qBACE,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gDACvC,WAAW,CAAC,0HAA0H,EACpI,cACI,oCACA,kBACJ;gDACF,OAAO;oDACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACF;oDACJ,QAAQ,WACH,kBAAkB,SAAS,qCAAqC,qCACjE;oDACJ,WAAW,WACN,kBAAkB,SACjB,8EACA,+EACF;gDACN;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,kBAAkB,SACjD,4BACA;wDACJ,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,SAC7C,uCACA;oDACN;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;gDACF;0DAEC,4BACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;wDAC5C,QAAQ,WAAW,8CAA8C;oDACnE;;;;;yEAGF;;sEACE,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACD,kBAAkB,SACjB,mFACA;gEACN,WAAW,WACN,kBAAkB,SACjB,6EACA,6EACD,kBAAkB,SACjB,4CACA;4DACR;sEAEA,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,QAAQ,WAAW,8CAA8C;gEACnE;;;;;;;;;;;sEAGJ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,YAAY,WAAW,QAAQ;gEACjC;0EAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;;4CAOpB,6BACC,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,OAAO,kBAAkB,SAAS,YAAY;oDAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oDAC1E,WAAW,kBAAkB,SACzB,kCACA;gDACN;;kEAEA,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,WAAW;4DACX,cAAc;4DACd,aAAa,CAAC,UAAU,EAAE,kBAAkB,SAAS,YAAY,WAAW;wDAC9E;;;;;;;;;;;;;uCArHE,KAAK,EAAE;;;;;gCA2HrB;;;;;;;;;;;;;;;;;8BAMA,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,WAAW,kBAAkB,SACzB,uCACA;wBACJ,WAAW,kBAAkB,SACzB,2EACA;oBACN;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCAEC,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,WAAW;gCACb;;kDAEA,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,YAAY;wCACd;;;;;;;;;;;;;;;;iDAKN;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;gDACZ,WAAW;4CACb;;8DAEA,6LAAC;oDAAK,WAAU;8DAA6C;;;;;;8DAC7D,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY;oDACd;;;;;;;;;;;;sDAGJ,6LAAC;;8DACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oDAC1E;8DACD;;;;;;8DAGD,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,eAAe;oDACjB;8DACD;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;wCACvE,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oCAC5E;8CACD;;;;;;;;;;;;;;;;;;gBASR,CAAC,6BACA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,OAAO,kBAAkB,SAAS,YAAY;oBAChD;8BAEA,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAsB;;;;;;0CACrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC/F,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/G;GA/YwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DashboardStats.tsx"], "sourcesContent": ["'use client'\n\nimport { Package, Users, DollarSign, AlertTriangle } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nimport type { DashboardStats } from '@/types'\n\ninterface DashboardStatsProps {\n  stats: DashboardStats\n}\n\nexport default function DashboardStats({ stats }: DashboardStatsProps) {\n  const { resolvedTheme } = useTheme()\n\n  const statCards = [\n    {\n      title: 'Products in List',\n      value: stats.totalProducts,\n      icon: Package,\n      color: 'bg-blue-500',\n      textColor: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      title: 'Customer Debts',\n      value: stats.totalDebts,\n      icon: Users,\n      color: 'bg-green-500',\n      textColor: 'text-green-600',\n      bgColor: 'bg-green-50',\n    },\n    {\n      title: 'Total Debt Amount',\n      value: `₱${stats.totalDebtAmount.toFixed(2)}`,\n      icon: DollarSign,\n      color: 'bg-yellow-500',\n      textColor: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      title: 'Low Stock Items',\n      value: stats.lowStockItems,\n      icon: AlertTriangle,\n      color: 'bg-red-500',\n      textColor: 'text-red-600',\n      bgColor: 'bg-red-50',\n    },\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <div\n              key={index}\n              className=\"rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n              }}\n            >\n              <div className=\"flex items-center\">\n                <div className={`p-3 rounded-lg ${card.bgColor} ${resolvedTheme === 'dark' ? 'opacity-90' : ''}`}>\n                  <Icon className={`h-6 w-6 ${card.textColor}`} />\n                </div>\n                <div className=\"ml-4\">\n                  <p\n                    className=\"text-sm font-medium transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                    }}\n                  >\n                    {card.title}\n                  </p>\n                  <p\n                    className=\"text-2xl font-semibold transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                    }}\n                  >\n                    {card.value}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div\n        className=\"rounded-lg shadow-md p-6 transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n        }}\n      >\n        <h3\n          className=\"text-lg font-semibold mb-4 transition-colors duration-300\"\n          style={{\n            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n          }}\n        >\n          Quick Actions\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button\n            className=\"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n          >\n            <Package className=\"h-8 w-8 text-blue-600 mr-3\" />\n            <div className=\"text-left\">\n              <p\n                className=\"font-medium transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                Add to Product List\n              </p>\n              <p\n                className=\"text-sm transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                Add a new product to your list\n              </p>\n            </div>\n          </button>\n          <button\n            className=\"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n          >\n            <Users className=\"h-8 w-8 text-green-600 mr-3\" />\n            <div className=\"text-left\">\n              <p\n                className=\"font-medium transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                Record New Debt\n              </p>\n              <p\n                className=\"text-sm transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                Add a new customer debt record\n              </p>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Store Overview */}\n      <div\n        className=\"rounded-lg shadow-md p-6 transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n        }}\n      >\n        <h3\n          className=\"text-lg font-semibold mb-4 transition-colors duration-300\"\n          style={{\n            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n          }}\n        >\n          Store Overview\n        </h3>\n        <div className=\"space-y-4\">\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Products in List\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              {stats.totalProducts}\n            </span>\n          </div>\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Outstanding Debts\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              {stats.totalDebts}\n            </span>\n          </div>\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Total Amount Owed\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              ₱{stats.totalDebtAmount.toFixed(2)}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center py-2\">\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Items Need Restocking\n            </span>\n            <span className={`font-semibold ${stats.lowStockItems > 0 ? 'text-red-600' : 'text-green-600'}`}>\n              {stats.lowStockItems}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAWe,SAAS,eAAe,EAAE,KAAK,EAAuB;;IACnE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI;YAC7C,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;YACX,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,kBAAkB,SAAS,eAAe,IAAI;8CAC9F,cAAA,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAEC,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBA1BZ;;;;;gBAgCX;;;;;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gBAC3D;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCACD;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;;kDAEA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;;;;;;;;;;;;;0CAKL,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;;kDAEA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gBAC3D;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCACD;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,MAAM,aAAa;;;;;;;;;;;;0CAGxB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,MAAM,UAAU;;;;;;;;;;;;0CAGrB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;4CACD;4CACG,MAAM,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;0CAGpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCAAK,WAAW,CAAC,cAAc,EAAE,MAAM,aAAa,GAAG,IAAI,iBAAiB,kBAAkB;kDAC5F,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GA9QwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/APIGraphing.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport ReactECharts from 'echarts-for-react'\nimport { TrendingUp, DollarSign, Package, Users, Calendar } from 'lucide-react'\n\nimport type { DashboardStats } from '@/types'\n\ninterface APIGraphingProps {\n  stats: DashboardStats\n}\n\nexport default function APIGraphing({ stats }: APIGraphingProps) {\n  const [salesData, setSalesData] = useState<number[]>([])\n  const [debtData, setDebtData] = useState<number[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n\n  // Advanced data generation with realistic patterns\n  const generateAdvancedData = useCallback(() => {\n    setIsLoading(true)\n\n    setTimeout(() => {\n      // Generate sales data with seasonal patterns\n      const salesData = Array.from({ length: 12 }, (_, i) => {\n        const baseValue = 30000\n        const seasonalFactor = Math.sin((i / 12) * 2 * Math.PI) * 10000\n        const randomFactor = (Math.random() - 0.5) * 8000\n        return Math.max(15000, Math.floor(baseValue + seasonalFactor + randomFactor))\n      })\n\n      // Generate debt data with weekly patterns\n      const debtData = Array.from({ length: 7 }, (_, i) => {\n        const baseValue = 8000\n        const weekendFactor = (i === 0 || i === 6) ? -2000 : 0\n        const randomFactor = (Math.random() - 0.5) * 3000\n        return Math.max(3000, Math.floor(baseValue + weekendFactor + randomFactor))\n      })\n\n      // Generate category data\n      const categoryData = [\n        { value: 35, name: 'Snacks', itemStyle: { color: '#22c55e' } },\n        { value: 25, name: 'Beverages', itemStyle: { color: '#facc15' } },\n        { value: 20, name: 'Canned Goods', itemStyle: { color: '#3b82f6' } },\n        { value: 12, name: 'Personal Care', itemStyle: { color: '#f59e0b' } },\n        { value: 8, name: 'Others', itemStyle: { color: '#8b5cf6' } }\n      ]\n\n      // Generate trend data (last 30 days)\n      const trendData = Array.from({ length: 30 }, (_, i) => {\n        const baseValue = 2500\n        const trendFactor = i * 50 // Growing trend\n        const randomFactor = (Math.random() - 0.5) * 500\n        return Math.max(1000, Math.floor(baseValue + trendFactor + randomFactor))\n      })\n\n      // Generate hourly data (24 hours)\n      const hourlyData = Array.from({ length: 24 }, (_, i) => {\n        const baseValue = 1000\n        const peakHours = [9, 10, 11, 14, 15, 16, 19, 20] // Business hours\n        const peakFactor = peakHours.includes(i) ? 800 : 0\n        const randomFactor = (Math.random() - 0.5) * 300\n        return Math.max(200, Math.floor(baseValue + peakFactor + randomFactor))\n      })\n\n      // Generate weekly data\n      const weeklyData = Array.from({ length: 52 }, (_, i) => {\n        const baseValue = 25000\n        const seasonalFactor = Math.sin((i / 52) * 2 * Math.PI) * 8000\n        const randomFactor = (Math.random() - 0.5) * 5000\n        return Math.max(12000, Math.floor(baseValue + seasonalFactor + randomFactor))\n      })\n\n      setChartData({\n        salesData,\n        debtData,\n        categoryData,\n        trendData,\n        hourlyData,\n        weeklyData\n      })\n\n      setLastUpdated(new Date())\n      setIsLoading(false)\n    }, 800)\n  }, [])\n\n  useEffect(() => {\n    generateAdvancedData()\n  }, [generateAdvancedData])\n\n  // Auto-refresh functionality\n  useEffect(() => {\n    if (!autoRefresh) return\n\n    const interval = setInterval(() => {\n      generateAdvancedData()\n    }, 30000) // Refresh every 30 seconds\n\n    return () => clearInterval(interval)\n  }, [autoRefresh, generateAdvancedData])\n\n  // Advanced chart configurations with theme support\n  const getChartTheme = () => ({\n    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n    textStyle: {\n      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937',\n      fontFamily: 'Inter, system-ui, sans-serif'\n    },\n    grid: {\n      borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n    }\n  })\n\n  // Enhanced Sales Chart with advanced features\n  const salesChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Monthly Sales Revenue',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const percentage = chartData.salesData.length > 1\n          ? ((data.value - chartData.salesData[Math.max(0, data.dataIndex - 1)]) / chartData.salesData[Math.max(0, data.dataIndex - 1)] * 100).toFixed(1)\n          : '0'\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"color: #22c55e;\">Revenue: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${percentage.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;\">\n              ${percentage.startsWith('-') ? '' : '+'}${percentage}% from previous month\n            </div>\n          </div>\n        `\n      },\n      axisPointer: {\n        type: 'cross',\n        crossStyle: {\n          color: '#22c55e'\n        }\n      }\n    },\n    legend: {\n      data: ['Revenue', 'Target'],\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Revenue',\n        data: chartData.salesData,\n        type: filters.chartType,\n        smooth: true,\n        lineStyle: {\n          color: '#22c55e',\n          width: 3,\n        },\n        itemStyle: {\n          color: '#22c55e',\n          borderRadius: filters.chartType === 'bar' ? [4, 4, 0, 0] : 0\n        },\n        areaStyle: filters.chartType === 'area' ? {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(34, 197, 94, 0.4)' },\n              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },\n            ],\n          },\n        } : undefined,\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(34, 197, 94, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Max' },\n            { type: 'min', name: 'Min' }\n          ],\n          itemStyle: {\n            color: '#facc15'\n          }\n        },\n        markLine: {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#f59e0b',\n            type: 'dashed'\n          }\n        }\n      },\n      {\n        name: 'Target',\n        data: chartData.salesData.map(val => val * 1.2),\n        type: 'line',\n        lineStyle: {\n          color: '#facc15',\n          width: 2,\n          type: 'dashed'\n        },\n        itemStyle: {\n          color: '#facc15'\n        },\n        symbol: 'none'\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '8%',\n      top: '15%',\n      containLabel: true,\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 0,\n        end: 100\n      },\n      {\n        start: 0,\n        end: 100,\n        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z',\n        handleSize: '80%',\n        handleStyle: {\n          color: '#22c55e',\n          shadowBlur: 3,\n          shadowColor: 'rgba(0, 0, 0, 0.6)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      }\n    ],\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.salesData, filters.chartType, resolvedTheme])\n\n  // Enhanced Debt Chart with advanced features\n  const debtChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Weekly Customer Debt Trends',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const avgDebt = chartData.debtData.reduce((a, b) => a + b, 0) / chartData.debtData.length\n        const comparison = ((data.value - avgDebt) / avgDebt * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"color: #facc15;\">Total Debt: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${comparison.startsWith('-') ? '#22c55e' : '#ef4444'}; font-size: 12px;\">\n              ${comparison.startsWith('-') ? '' : '+'}${comparison}% vs average\n            </div>\n          </div>\n        `\n      },\n      axisPointer: {\n        type: 'shadow'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n        rotate: 45\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        data: chartData.debtData,\n        type: 'bar',\n        barWidth: '60%',\n        itemStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: '#facc15' },\n              { offset: 0.5, color: '#f59e0b' },\n              { offset: 1, color: '#eab308' },\n            ],\n          },\n          borderRadius: [4, 4, 0, 0]\n        },\n        emphasis: {\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [\n                { offset: 0, color: '#fbbf24' },\n                { offset: 1, color: '#d97706' },\n              ],\n            },\n            shadowBlur: 10,\n            shadowColor: 'rgba(245, 158, 11, 0.5)'\n          },\n        },\n        markLine: {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#ef4444',\n            type: 'dashed'\n          }\n        }\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '15%',\n      top: '15%',\n      containLabel: true,\n    },\n    animation: true,\n    animationDuration: 1000,\n    animationEasing: 'cubicOut'\n  }), [chartData.debtData, resolvedTheme])\n\n  // Enhanced Category Chart with interactive features\n  const categoryChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Product Categories Distribution',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'item',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const total = chartData.categoryData.reduce((sum, item) => sum + item.value, 0)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${params.name}</div>\n            <div style=\"color: ${params.color};\">Products: ${params.value}</div>\n            <div style=\"color: #6b7280; font-size: 12px;\">\n              ${params.percent}% of total (${total} products)\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      orient: 'horizontal',\n      bottom: 10,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      }\n    },\n    series: [\n      {\n        name: 'Categories',\n        type: 'pie',\n        radius: ['45%', '75%'],\n        center: ['50%', '45%'],\n        avoidLabelOverlap: false,\n        itemStyle: {\n          borderRadius: 8,\n          borderColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          borderWidth: 3,\n          shadowBlur: 10,\n          shadowColor: 'rgba(0, 0, 0, 0.1)'\n        },\n        label: {\n          show: false,\n          position: 'center',\n        },\n        emphasis: {\n          label: {\n            show: true,\n            fontSize: 24,\n            fontWeight: 'bold',\n            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n          },\n          itemStyle: {\n            shadowBlur: 20,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.3)'\n          },\n          scale: true,\n          scaleSize: 5\n        },\n        labelLine: {\n          show: false,\n        },\n        data: chartData.categoryData,\n        animationType: 'scale',\n        animationEasing: 'elasticOut',\n        animationDelay: (idx: number) => idx * 100\n      },\n    ],\n  }), [chartData.categoryData, resolvedTheme])\n\n  // New Trend Analysis Chart\n  const trendChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Daily Sales Trend (Last 30 Days)',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const dayIndex = data.dataIndex\n        const previousValue = dayIndex > 0 ? chartData.trendData[dayIndex - 1] : data.value\n        const change = ((data.value - previousValue) / previousValue * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">Day ${dayIndex + 1}</div>\n            <div style=\"color: #3b82f6;\">Sales: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${change.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;\">\n              ${change.startsWith('-') ? '' : '+'}${change}% from previous day\n            </div>\n          </div>\n        `\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n        interval: 4\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        data: chartData.trendData,\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          color: '#3b82f6',\n          width: 3,\n        },\n        itemStyle: {\n          color: '#3b82f6',\n        },\n        areaStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },\n              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },\n            ],\n          },\n        },\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(59, 130, 246, 0.5)'\n          }\n        }\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '8%',\n      top: '15%',\n      containLabel: true,\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 70,\n        end: 100\n      }\n    ]\n  }), [chartData.trendData, resolvedTheme])\n\n  // Enhanced KPI calculations with advanced metrics\n  const kpiCards = useMemo(() => {\n    const totalRevenue = chartData.salesData.reduce((a, b) => a + b, 0)\n    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length\n    const totalDebt = chartData.debtData.reduce((a, b) => a + b, 0)\n    const avgDailyDebt = totalDebt / chartData.debtData.length\n\n    return [\n      {\n        title: 'Total Revenue',\n        value: '₱' + totalRevenue.toLocaleString(),\n        icon: DollarSign,\n        color: 'text-green-600 dark:text-green-400',\n        bgColor: 'bg-green-50 dark:bg-green-900/20',\n        change: '+12.5%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`,\n        trend: 'up'\n      },\n      {\n        title: 'Products Listed',\n        value: stats.totalProducts.toString(),\n        icon: Package,\n        color: 'text-blue-600 dark:text-blue-400',\n        bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n        change: '+5.2%',\n        changeColor: 'text-blue-600 dark:text-blue-400',\n        subtitle: `${stats.lowStockItems} low stock`,\n        trend: 'up'\n      },\n      {\n        title: 'Active Customers',\n        value: stats.totalDebts.toString(),\n        icon: Users,\n        color: 'text-purple-600 dark:text-purple-400',\n        bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n        change: '+8.1%',\n        changeColor: 'text-purple-600 dark:text-purple-400',\n        subtitle: 'With outstanding debt',\n        trend: 'up'\n      },\n      {\n        title: 'Outstanding Debt',\n        value: '₱' + stats.totalDebtAmount.toLocaleString(),\n        icon: TrendingUp,\n        color: 'text-yellow-600 dark:text-yellow-400',\n        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n        change: '-3.2%',\n        changeColor: 'text-red-600 dark:text-red-400',\n        subtitle: `Avg: ₱${avgDailyDebt.toLocaleString()}/day`,\n        trend: 'down'\n      },\n    ]\n  }, [chartData, stats])\n\n  // Filter and export handlers\n  const handleFilterChange = (key: keyof FilterState, value: any) => {\n    setFilters(prev => ({ ...prev, [key]: value }))\n  }\n\n  const handleExport = (format: 'pdf' | 'excel' | 'csv' | 'png') => {\n    setShowExportMenu(false)\n    // Simulate export process\n    setIsLoading(true)\n    setTimeout(() => {\n      setIsLoading(false)\n      // In a real app, this would trigger the actual export\n      console.log(`Exporting data as ${format}`)\n    }, 2000)\n  }\n\n  const refreshData = () => {\n    generateAdvancedData()\n  }\n\n  const getActiveChartOption = () => {\n    switch (activeChart) {\n      case 'sales':\n        return salesChartOption\n      case 'debt':\n        return debtChartOption\n      case 'category':\n        return categoryChartOption\n      case 'trend':\n        return trendChartOption\n      default:\n        return salesChartOption\n    }\n  }\n\n  return (\n    <div className=\"space-y-6 animate-fade-in\">\n      {/* Header with Controls */}\n      <div className=\"card p-6\">\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              API Graphing & Visuals\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n              Advanced analytics and business insights dashboard\n            </p>\n          </div>\n\n          <div className=\"flex flex-wrap items-center gap-3\">\n            {/* Auto Refresh Toggle */}\n            <button\n              onClick={() => setAutoRefresh(!autoRefresh)}\n              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                autoRefresh\n                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'\n                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'\n              }`}\n            >\n              <Activity className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />\n              Auto Refresh\n            </button>\n\n            {/* Manual Refresh */}\n            <button\n              onClick={refreshData}\n              disabled={isLoading}\n              className=\"flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50\"\n            >\n              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n              Refresh\n            </button>\n\n            {/* Filters Toggle */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded-lg text-sm font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all duration-200\"\n            >\n              <Filter className=\"h-4 w-4\" />\n              Filters\n              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />\n            </button>\n\n            {/* Export Menu */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowExportMenu(!showExportMenu)}\n                className=\"flex items-center gap-2 px-3 py-2 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg text-sm font-medium hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200\"\n              >\n                <Download className=\"h-4 w-4\" />\n                Export\n              </button>\n\n              {showExportMenu && (\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10\">\n                  <div className=\"p-2\">\n                    <button\n                      onClick={() => handleExport('pdf')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileText className=\"h-4 w-4\" />\n                      Export as PDF\n                    </button>\n                    <button\n                      onClick={() => handleExport('excel')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileSpreadsheet className=\"h-4 w-4\" />\n                      Export as Excel\n                    </button>\n                    <button\n                      onClick={() => handleExport('csv')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileText className=\"h-4 w-4\" />\n                      Export as CSV\n                    </button>\n                    <button\n                      onClick={() => handleExport('png')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <Image className=\"h-4 w-4\" />\n                      Export as PNG\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Filters Panel */}\n        {showFilters && (\n          <div className=\"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 animate-fade-in-up\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Date Range Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Date Range\n                </label>\n                <select\n                  value={filters.dateRange}\n                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"today\">Today</option>\n                  <option value=\"week\">This Week</option>\n                  <option value=\"month\">This Month</option>\n                  <option value=\"quarter\">This Quarter</option>\n                  <option value=\"year\">This Year</option>\n                  <option value=\"custom\">Custom Range</option>\n                </select>\n              </div>\n\n              {/* Category Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Category\n                </label>\n                <select\n                  value={filters.category}\n                  onChange={(e) => handleFilterChange('category', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Categories</option>\n                  <option value=\"snacks\">Snacks</option>\n                  <option value=\"beverages\">Beverages</option>\n                  <option value=\"canned\">Canned Goods</option>\n                  <option value=\"personal\">Personal Care</option>\n                  <option value=\"others\">Others</option>\n                </select>\n              </div>\n\n              {/* Chart Type Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Chart Type\n                </label>\n                <select\n                  value={filters.chartType}\n                  onChange={(e) => handleFilterChange('chartType', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"line\">Line Chart</option>\n                  <option value=\"bar\">Bar Chart</option>\n                  <option value=\"area\">Area Chart</option>\n                </select>\n              </div>\n\n              {/* Search Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Search\n                </label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search data...\"\n                    className=\"w-full pl-10 pr-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced KPI Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {kpiCards.map((kpi, index) => (\n          <div\n            key={index}\n            className=\"card p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                  {kpi.title}\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white mt-1\">\n                  {kpi.value}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                  {kpi.subtitle}\n                </p>\n                <div className=\"flex items-center gap-1 mt-2\">\n                  <span className={`text-sm font-medium ${kpi.changeColor}`}>\n                    {kpi.change}\n                  </span>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    from last month\n                  </span>\n                </div>\n              </div>\n              <div className={`p-3 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-300`}>\n                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />\n              </div>\n            </div>\n\n            {/* Mini trend indicator */}\n            <div className=\"mt-4 flex items-center justify-between\">\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  kpi.trend === 'up' ? 'bg-green-500' : 'bg-red-500'\n                } animate-pulse`}></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {kpi.trend === 'up' ? 'Trending up' : 'Trending down'}\n                </span>\n              </div>\n              <Eye className=\"h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Chart Navigation Tabs */}\n      <div className=\"card p-6\">\n        <div className=\"flex flex-wrap items-center gap-2 mb-6\">\n          {[\n            { id: 'sales', label: 'Sales Revenue', icon: LineChart },\n            { id: 'debt', label: 'Customer Debt', icon: BarChart3 },\n            { id: 'category', label: 'Categories', icon: PieChart },\n            { id: 'trend', label: 'Daily Trend', icon: TrendingUp }\n          ].map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveChart(tab.id as any)}\n              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                activeChart === tab.id\n                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-md'\n                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'\n              }`}\n            >\n              <tab.icon className=\"h-4 w-4\" />\n              {tab.label}\n            </button>\n          ))}\n\n          <div className=\"ml-auto flex items-center gap-2\">\n            <button\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\"\n              title=\"Fullscreen\"\n            >\n              <Maximize2 className=\"h-4 w-4\" />\n            </button>\n            <button\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\"\n              title=\"Chart Settings\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Main Chart Display */}\n        <div className=\"relative\">\n          {isLoading && (\n            <div className=\"absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-lg\">\n              <div className=\"flex items-center gap-3\">\n                <RefreshCw className=\"h-5 w-5 animate-spin text-green-600\" />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Loading chart data...\n                </span>\n              </div>\n            </div>\n          )}\n\n          <div className=\"transition-opacity duration-300\" style={{ opacity: isLoading ? 0.5 : 1 }}>\n            <ReactECharts\n              option={getActiveChartOption()}\n              style={{ height: '500px' }}\n              opts={{ renderer: 'svg' }}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Additional Charts Grid */}\n      {activeChart === 'sales' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in\">\n          {/* Hourly Sales Pattern */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Hourly Sales Pattern\n            </h3>\n            <ReactECharts\n              option={{\n                ...getChartTheme(),\n                tooltip: {\n                  trigger: 'axis',\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n                  textStyle: {\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }\n                },\n                xAxis: {\n                  type: 'category',\n                  data: Array.from({ length: 24 }, (_, i) => `${i}:00`),\n                  axisLabel: {\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n                    interval: 3\n                  }\n                },\n                yAxis: {\n                  type: 'value',\n                  axisLabel: {\n                    formatter: '₱{value}',\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }\n                },\n                series: [{\n                  data: chartData.hourlyData,\n                  type: 'bar',\n                  itemStyle: {\n                    color: '#8b5cf6',\n                    borderRadius: [2, 2, 0, 0]\n                  }\n                }],\n                grid: {\n                  left: '3%',\n                  right: '4%',\n                  bottom: '8%',\n                  top: '5%',\n                  containLabel: true\n                }\n              }}\n              style={{ height: '300px' }}\n            />\n          </div>\n\n          {/* Weekly Performance */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Weekly Performance\n            </h3>\n            <ReactECharts\n              option={{\n                ...getChartTheme(),\n                tooltip: {\n                  trigger: 'axis',\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n                  textStyle: {\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }\n                },\n                xAxis: {\n                  type: 'category',\n                  data: Array.from({ length: 52 }, (_, i) => `W${i + 1}`),\n                  axisLabel: {\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n                    interval: 7\n                  }\n                },\n                yAxis: {\n                  type: 'value',\n                  axisLabel: {\n                    formatter: '₱{value}',\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }\n                },\n                series: [{\n                  data: chartData.weeklyData,\n                  type: 'line',\n                  smooth: true,\n                  lineStyle: {\n                    color: '#f59e0b',\n                    width: 2\n                  },\n                  itemStyle: {\n                    color: '#f59e0b'\n                  },\n                  areaStyle: {\n                    color: {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 0,\n                      y2: 1,\n                      colorStops: [\n                        { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },\n                        { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }\n                      ]\n                    }\n                  }\n                }],\n                grid: {\n                  left: '3%',\n                  right: '4%',\n                  bottom: '8%',\n                  top: '5%',\n                  containLabel: true\n                },\n                dataZoom: [{\n                  type: 'inside',\n                  start: 80,\n                  end: 100\n                }]\n              }}\n              style={{ height: '300px' }}\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Real-time Status Footer */}\n      <div className=\"card p-4\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className={`w-3 h-3 rounded-full animate-pulse ${\n              autoRefresh ? 'bg-green-500' : 'bg-gray-400'\n            }`}></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {autoRefresh ? 'Real-time Data Updates Active' : 'Real-time Updates Paused'}\n            </span>\n            {isLoading && (\n              <div className=\"flex items-center gap-2\">\n                <RefreshCw className=\"h-4 w-4 animate-spin text-blue-600\" />\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">Updating...</span>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-2\">\n              <Calendar className=\"h-4 w-4\" />\n              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Activity className=\"h-4 w-4\" />\n              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAYe,SAAS,YAAY,EAAE,KAAK,EAAoB;;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,mDAAmD;IACnD,MAAM,uBAAuB;yDAAY;YACvC,aAAa;YAEb;iEAAW;oBACT,6CAA6C;oBAC7C,MAAM,YAAY,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG;mFAAG,CAAC,GAAG;4BAC/C,MAAM,YAAY;4BAClB,MAAM,iBAAiB,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,IAAI,KAAK,EAAE,IAAI;4BAC1D,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC7C,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,iBAAiB;wBACjE;;oBAEA,0CAA0C;oBAC1C,MAAM,WAAW,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE;kFAAG,CAAC,GAAG;4BAC7C,MAAM,YAAY;4BAClB,MAAM,gBAAgB,AAAC,MAAM,KAAK,MAAM,IAAK,CAAC,OAAO;4BACrD,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC7C,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,gBAAgB;wBAC/D;;oBAEA,yBAAyB;oBACzB,MAAM,eAAe;wBACnB;4BAAE,OAAO;4BAAI,MAAM;4BAAU,WAAW;gCAAE,OAAO;4BAAU;wBAAE;wBAC7D;4BAAE,OAAO;4BAAI,MAAM;4BAAa,WAAW;gCAAE,OAAO;4BAAU;wBAAE;wBAChE;4BAAE,OAAO;4BAAI,MAAM;4BAAgB,WAAW;gCAAE,OAAO;4BAAU;wBAAE;wBACnE;4BAAE,OAAO;4BAAI,MAAM;4BAAiB,WAAW;gCAAE,OAAO;4BAAU;wBAAE;wBACpE;4BAAE,OAAO;4BAAG,MAAM;4BAAU,WAAW;gCAAE,OAAO;4BAAU;wBAAE;qBAC7D;oBAED,qCAAqC;oBACrC,MAAM,YAAY,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG;mFAAG,CAAC,GAAG;4BAC/C,MAAM,YAAY;4BAClB,MAAM,cAAc,IAAI,GAAG,gBAAgB;;4BAC3C,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC7C,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,cAAc;wBAC7D;;oBAEA,kCAAkC;oBAClC,MAAM,aAAa,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG;oFAAG,CAAC,GAAG;4BAChD,MAAM,YAAY;4BAClB,MAAM,YAAY;gCAAC;gCAAG;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;6BAAG,CAAC,iBAAiB;;4BACnE,MAAM,aAAa,UAAU,QAAQ,CAAC,KAAK,MAAM;4BACjD,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC7C,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,aAAa;wBAC3D;;oBAEA,uBAAuB;oBACvB,MAAM,aAAa,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG;oFAAG,CAAC,GAAG;4BAChD,MAAM,YAAY;4BAClB,MAAM,iBAAiB,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,IAAI,KAAK,EAAE,IAAI;4BAC1D,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC7C,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,iBAAiB;wBACjE;;oBAEA,aAAa;wBACX;wBACA;wBACA;wBACA;wBACA;wBACA;oBACF;oBAEA,eAAe,IAAI;oBACnB,aAAa;gBACf;gEAAG;QACL;wDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAqB;IAEzB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,aAAa;YAElB,MAAM,WAAW;kDAAY;oBAC3B;gBACF;iDAAG,OAAO,2BAA2B;;YAErC;yCAAO,IAAM,cAAc;;QAC7B;gCAAG;QAAC;QAAa;KAAqB;IAEtC,mDAAmD;IACnD,MAAM,gBAAgB,IAAM,CAAC;YAC3B,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,WAAW;gBACT,OAAO,kBAAkB,SAAS,YAAY;gBAC9C,YAAY;YACd;YACA,MAAM;gBACJ,aAAa,kBAAkB,SAAS,YAAY;YACtD;QACF,CAAC;IAED,8CAA8C;IAC9C,MAAM,mBAAmB;iDAAQ,IAAM,CAAC;gBACtC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;iEAAE,CAAC;4BACV,MAAM,OAAO,MAAM,CAAC,EAAE;4BACtB,MAAM,aAAa,UAAU,SAAS,CAAC,MAAM,GAAG,IAC5C,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,SAAS,GAAG,GAAG,IAAI,UAAU,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,KAC3I;4BACJ,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;mDACzB,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BAClD,EAAE,WAAW,UAAU,CAAC,OAAO,YAAY,UAAU;cACtE,EAAE,WAAW,UAAU,CAAC,OAAO,KAAK,MAAM,WAAW;;;QAG3D,CAAC;wBACH;;oBACA,aAAa;wBACX,MAAM;wBACN,YAAY;4BACV,OAAO;wBACT;oBACF;gBACF;gBACA,QAAQ;oBACN,MAAM;wBAAC;wBAAW;qBAAS;oBAC3B,KAAK;oBACL,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM;oBAC1F,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM,UAAU,SAAS;wBACzB,MAAM,QAAQ,SAAS;wBACvB,QAAQ;wBACR,WAAW;4BACT,OAAO;4BACP,OAAO;wBACT;wBACA,WAAW;4BACT,OAAO;4BACP,cAAc,QAAQ,SAAS,KAAK,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE,GAAG;wBAC7D;wBACA,WAAW,QAAQ,SAAS,KAAK,SAAS;4BACxC,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAAyB;oCAC7C;wCAAE,QAAQ;wCAAG,OAAO;oCAA0B;iCAC/C;4BACH;wBACF,IAAI;wBACJ,UAAU;4BACR,OAAO;4BACP,WAAW;gCACT,YAAY;gCACZ,aAAa;4BACf;wBACF;wBACA,WAAW;4BACT,MAAM;gCACJ;oCAAE,MAAM;oCAAO,MAAM;gCAAM;gCAC3B;oCAAE,MAAM;oCAAO,MAAM;gCAAM;6BAC5B;4BACD,WAAW;gCACT,OAAO;4BACT;wBACF;wBACA,UAAU;4BACR,MAAM;gCACJ;oCAAE,MAAM;oCAAW,MAAM;gCAAU;6BACpC;4BACD,WAAW;gCACT,OAAO;gCACP,MAAM;4BACR;wBACF;oBACF;oBACA;wBACE,MAAM;wBACN,MAAM,UAAU,SAAS,CAAC,GAAG;qEAAC,CAAA,MAAO,MAAM;;wBAC3C,MAAM;wBACN,WAAW;4BACT,OAAO;4BACP,OAAO;4BACP,MAAM;wBACR;wBACA,WAAW;4BACT,OAAO;wBACT;wBACA,QAAQ;oBACV;iBACD;gBACD,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;gBAChB;gBACA,UAAU;oBACR;wBACE,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,YAAY;wBACZ,aAAa;4BACX,OAAO;4BACP,YAAY;4BACZ,aAAa;4BACb,eAAe;4BACf,eAAe;wBACjB;oBACF;iBACD;gBACD,SAAS;oBACP,SAAS;wBACP,UAAU;4BACR,YAAY;wBACd;wBACA,SAAS,CAAC;wBACV,aAAa;4BACX,YAAY;wBACd;oBACF;oBACA,WAAW;wBACT,aAAa,kBAAkB,SAAS,YAAY;oBACtD;gBACF;YACF,CAAC;gDAAG;QAAC,UAAU,SAAS;QAAE,QAAQ,SAAS;QAAE;KAAc;IAE3D,6CAA6C;IAC7C,MAAM,kBAAkB;gDAAQ,IAAM,CAAC;gBACrC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;gEAAE,CAAC;4BACV,MAAM,OAAO,MAAM,CAAC,EAAE;4BACtB,MAAM,UAAU,UAAU,QAAQ,CAAC,MAAM;wEAAC,CAAC,GAAG,IAAM,IAAI;uEAAG,KAAK,UAAU,QAAQ,CAAC,MAAM;4BACzF,MAAM,aAAa,CAAC,CAAC,KAAK,KAAK,GAAG,OAAO,IAAI,UAAU,GAAG,EAAE,OAAO,CAAC;4BACpE,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;sDACtB,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BACrD,EAAE,WAAW,UAAU,CAAC,OAAO,YAAY,UAAU;cACtE,EAAE,WAAW,UAAU,CAAC,OAAO,KAAK,MAAM,WAAW;;;QAG3D,CAAC;wBACH;;oBACA,aAAa;wBACX,MAAM;oBACR;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;wBAAC;wBAAU;wBAAW;wBAAa;wBAAY;wBAAU;wBAAY;qBAAS;oBACpF,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM,UAAU,QAAQ;wBACxB,MAAM;wBACN,UAAU;wBACV,WAAW;4BACT,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;oCAC9B;wCAAE,QAAQ;wCAAK,OAAO;oCAAU;oCAChC;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;iCAC/B;4BACH;4BACA,cAAc;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;wBAC5B;wBACA,UAAU;4BACR,WAAW;gCACT,OAAO;oCACL,MAAM;oCACN,GAAG;oCACH,GAAG;oCACH,IAAI;oCACJ,IAAI;oCACJ,YAAY;wCACV;4CAAE,QAAQ;4CAAG,OAAO;wCAAU;wCAC9B;4CAAE,QAAQ;4CAAG,OAAO;wCAAU;qCAC/B;gCACH;gCACA,YAAY;gCACZ,aAAa;4BACf;wBACF;wBACA,UAAU;4BACR,MAAM;gCACJ;oCAAE,MAAM;oCAAW,MAAM;gCAAU;6BACpC;4BACD,WAAW;gCACT,OAAO;gCACP,MAAM;4BACR;wBACF;oBACF;iBACD;gBACD,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;gBAChB;gBACA,WAAW;gBACX,mBAAmB;gBACnB,iBAAiB;YACnB,CAAC;+CAAG;QAAC,UAAU,QAAQ;QAAE;KAAc;IAEvC,oDAAoD;IACpD,MAAM,sBAAsB;oDAAQ,IAAM,CAAC;gBACzC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;oEAAE,CAAC;4BACV,MAAM,QAAQ,UAAU,YAAY,CAAC,MAAM;kFAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK;iFAAE;4BAC7E,OAAO,CAAC;;gEAEgD,EAAE,OAAO,IAAI,CAAC;+BAC/C,EAAE,OAAO,KAAK,CAAC,aAAa,EAAE,OAAO,KAAK,CAAC;;cAE5D,EAAE,OAAO,OAAO,CAAC,YAAY,EAAE,MAAM;;;QAG3C,CAAC;wBACH;;gBACF;gBACA,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM;wBACN,QAAQ;4BAAC;4BAAO;yBAAM;wBACtB,QAAQ;4BAAC;4BAAO;yBAAM;wBACtB,mBAAmB;wBACnB,WAAW;4BACT,cAAc;4BACd,aAAa,kBAAkB,SAAS,YAAY;4BACpD,aAAa;4BACb,YAAY;4BACZ,aAAa;wBACf;wBACA,OAAO;4BACL,MAAM;4BACN,UAAU;wBACZ;wBACA,UAAU;4BACR,OAAO;gCACL,MAAM;gCACN,UAAU;gCACV,YAAY;gCACZ,OAAO,kBAAkB,SAAS,YAAY;4BAChD;4BACA,WAAW;gCACT,YAAY;gCACZ,eAAe;gCACf,aAAa;4BACf;4BACA,OAAO;4BACP,WAAW;wBACb;wBACA,WAAW;4BACT,MAAM;wBACR;wBACA,MAAM,UAAU,YAAY;wBAC5B,eAAe;wBACf,iBAAiB;wBACjB,cAAc;wEAAE,CAAC,MAAgB,MAAM;;oBACzC;iBACD;YACH,CAAC;mDAAG;QAAC,UAAU,YAAY;QAAE;KAAc;IAE3C,2BAA2B;IAC3B,MAAM,mBAAmB;iDAAQ,IAAM,CAAC;gBACtC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;iEAAE,CAAC;4BACV,MAAM,OAAO,MAAM,CAAC,EAAE;4BACtB,MAAM,WAAW,KAAK,SAAS;4BAC/B,MAAM,gBAAgB,WAAW,IAAI,UAAU,SAAS,CAAC,WAAW,EAAE,GAAG,KAAK,KAAK;4BACnF,MAAM,SAAS,CAAC,CAAC,KAAK,KAAK,GAAG,aAAa,IAAI,gBAAgB,GAAG,EAAE,OAAO,CAAC;4BAC5E,OAAO,CAAC;;oEAEoD,EAAE,WAAW,EAAE;iDAClC,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BAChD,EAAE,OAAO,UAAU,CAAC,OAAO,YAAY,UAAU;cAClE,EAAE,OAAO,UAAU,CAAC,OAAO,KAAK,MAAM,OAAO;;;QAGnD,CAAC;wBACH;;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG;iEAAG,CAAC,GAAG,IAAM,CAAC,IAAI,EAAE,IAAI,GAAG;;oBACzD,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM,UAAU,SAAS;wBACzB,MAAM;wBACN,QAAQ;wBACR,WAAW;4BACT,OAAO;4BACP,OAAO;wBACT;wBACA,WAAW;4BACT,OAAO;wBACT;wBACA,WAAW;4BACT,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAA0B;oCAC9C;wCAAE,QAAQ;wCAAG,OAAO;oCAA2B;iCAChD;4BACH;wBACF;wBACA,UAAU;4BACR,OAAO;4BACP,WAAW;gCACT,YAAY;gCACZ,aAAa;4BACf;wBACF;oBACF;iBACD;gBACD,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;gBAChB;gBACA,UAAU;oBACR;wBACE,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;iBACD;YACH,CAAC;gDAAG;QAAC,UAAU,SAAS;QAAE;KAAc;IAExC,kDAAkD;IAClD,MAAM,WAAW;yCAAQ;YACvB,MAAM,eAAe,UAAU,SAAS,CAAC,MAAM;8DAAC,CAAC,GAAG,IAAM,IAAI;6DAAG;YACjE,MAAM,oBAAoB,eAAe,UAAU,SAAS,CAAC,MAAM;YACnE,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM;2DAAC,CAAC,GAAG,IAAM,IAAI;0DAAG;YAC7D,MAAM,eAAe,YAAY,UAAU,QAAQ,CAAC,MAAM;YAE1D,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO,MAAM,aAAa,cAAc;oBACxC,MAAM,qNAAA,CAAA,aAAU;oBAChB,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,UAAU,CAAC,MAAM,EAAE,kBAAkB,cAAc,GAAG,MAAM,CAAC;oBAC7D,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO,MAAM,aAAa,CAAC,QAAQ;oBACnC,MAAM,2MAAA,CAAA,UAAO;oBACb,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,UAAU,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC;oBAC5C,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO,MAAM,UAAU,CAAC,QAAQ;oBAChC,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,UAAU;oBACV,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO,MAAM,MAAM,eAAe,CAAC,cAAc;oBACjD,MAAM,qNAAA,CAAA,aAAU;oBAChB,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,UAAU,CAAC,MAAM,EAAE,aAAa,cAAc,GAAG,IAAI,CAAC;oBACtD,OAAO;gBACT;aACD;QACH;wCAAG;QAAC;QAAW;KAAM;IAErB,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC/C;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB;QAClB,0BAA0B;QAC1B,aAAa;QACb,WAAW;YACT,aAAa;YACb,sDAAsD;YACtD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC3C,GAAG;IACL;IAEA,MAAM,cAAc;QAClB;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,6FAA6F,EACvG,cACI,yEACA,iEACJ;;0DAEF,6LAAC;gDAAS,WAAW,CAAC,QAAQ,EAAE,cAAc,kBAAkB,IAAI;;;;;;4CAAI;;;;;;;kDAK1E,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAU,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;4CAAI;;;;;;;kDAKxE,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,6LAAC;gDAAO,WAAU;;;;;;4CAAY;0DAE9B,6LAAC;gDAAY,WAAW,CAAC,0CAA0C,EAAE,cAAc,eAAe,IAAI;;;;;;;;;;;;kDAIxG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,kBAAkB,CAAC;gDAClC,WAAU;;kEAEV,6LAAC;wDAAS,WAAU;;;;;;oDAAY;;;;;;;4CAIjC,gCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,6LAAC;oEAAS,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,6LAAC;oEAAgB,WAAU;;;;;;gEAAY;;;;;;;sEAGzC,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,6LAAC;oEAAS,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,6LAAC;oEAAM,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAW1C,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC9D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAKzB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;;;;;8DAClB,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxB,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DACV,IAAI,QAAQ;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,IAAI,WAAW,EAAE;kEACtD,IAAI,MAAM;;;;;;kEAEb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK/D,6LAAC;wCAAI,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,wDAAwD,CAAC;kDACrG,cAAA,6LAAC,IAAI,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;0CAK/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,IAAI,KAAK,KAAK,OAAO,iBAAiB,aACvC,cAAc,CAAC;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,KAAK,OAAO,gBAAgB;;;;;;;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;uBAtCZ;;;;;;;;;;0BA6CX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ;gCACC;oCAAE,IAAI;oCAAS,OAAO;oCAAiB,MAAM;gCAAU;gCACvD;oCAAE,IAAI;oCAAQ,OAAO;oCAAiB,MAAM;gCAAU;gCACtD;oCAAE,IAAI;oCAAY,OAAO;oCAAc,MAAM;gCAAS;gCACtD;oCAAE,IAAI;oCAAS,OAAO;oCAAe,MAAM,qNAAA,CAAA,aAAU;gCAAC;6BACvD,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;oCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;oCACpC,WAAW,CAAC,6FAA6F,EACvG,gBAAgB,IAAI,EAAE,GAClB,mFACA,6EACJ;;sDAEF,6LAAC,IAAI,IAAI;4CAAC,WAAU;;;;;;wCACnB,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;0CAaf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC;4CAAU,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCACC,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC;4CAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC;wBAAI,WAAU;;4BACZ,2BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAU,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAuD;;;;;;;;;;;;;;;;;0CAO7E,6LAAC;gCAAI,WAAU;gCAAkC,OAAO;oCAAE,SAAS,YAAY,MAAM;gCAAE;0CACrF,cAAA,6LAAC,0JAAA,CAAA,UAAY;oCACX,QAAQ;oCACR,OAAO;wCAAE,QAAQ;oCAAQ;oCACzB,MAAM;wCAAE,UAAU;oCAAM;;;;;;;;;;;;;;;;;;;;;;;YAO/B,gBAAgB,yBACf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;oCACN,GAAG,eAAe;oCAClB,SAAS;wCACP,SAAS;wCACT,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,MAAM,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG,IAAM,GAAG,EAAE,GAAG,CAAC;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;4CAC9C,UAAU;wCACZ;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,WAAW;4CACT,WAAW;4CACX,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,QAAQ;wCAAC;4CACP,MAAM,UAAU,UAAU;4CAC1B,MAAM;4CACN,WAAW;gDACT,OAAO;gDACP,cAAc;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE;4CAC5B;wCACF;qCAAE;oCACF,MAAM;wCACJ,MAAM;wCACN,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,cAAc;oCAChB;gCACF;gCACA,OAAO;oCAAE,QAAQ;gCAAQ;;;;;;;;;;;;kCAK7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;oCACN,GAAG,eAAe;oCAClB,SAAS;wCACP,SAAS;wCACT,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,MAAM,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,IAAI,GAAG;wCACtD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;4CAC9C,UAAU;wCACZ;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,WAAW;4CACT,WAAW;4CACX,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,QAAQ;wCAAC;4CACP,MAAM,UAAU,UAAU;4CAC1B,MAAM;4CACN,QAAQ;4CACR,WAAW;gDACT,OAAO;gDACP,OAAO;4CACT;4CACA,WAAW;gDACT,OAAO;4CACT;4CACA,WAAW;gDACT,OAAO;oDACL,MAAM;oDACN,GAAG;oDACH,GAAG;oDACH,IAAI;oDACJ,IAAI;oDACJ,YAAY;wDACV;4DAAE,QAAQ;4DAAG,OAAO;wDAA0B;wDAC9C;4DAAE,QAAQ;4DAAG,OAAO;wDAA2B;qDAChD;gDACH;4CACF;wCACF;qCAAE;oCACF,MAAM;wCACJ,MAAM;wCACN,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,cAAc;oCAChB;oCACA,UAAU;wCAAC;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;qCAAE;gCACJ;gCACA,OAAO;oCAAE,QAAQ;gCAAQ;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,mCAAmC,EAClD,cAAc,iBAAiB,eAC/B;;;;;;8CACF,6LAAC;oCAAK,WAAU;8CACb,cAAc,kCAAkC;;;;;;gCAElD,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAU,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;;gDAAK;gDAAe,YAAY,kBAAkB;;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAS,WAAU;;;;;;sDACpB,6LAAC;;gDAAK;gDAAc,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;GAhoCwB;KAAA", "debugId": null}}, {"offset": {"line": 3537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Upload, Package } from 'lucide-react'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductModalProps {\n  isOpen: boolean\n  onClose: () => void\n  product?: Product | null\n}\n\nexport default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    net_weight: '',\n    price: '',\n    stock_quantity: '',\n    category: '',\n    image_url: ''\n  })\n  const [imageFile, setImageFile] = useState<File | null>(null)\n  const [imagePreview, setImagePreview] = useState<string>('')\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        net_weight: product.net_weight,\n        price: product.price.toString(),\n        stock_quantity: product.stock_quantity.toString(),\n        category: product.category,\n        image_url: product.image_url || ''\n      })\n      setImagePreview(product.image_url || '')\n    } else {\n      setFormData({\n        name: '',\n        net_weight: '',\n        price: '',\n        stock_quantity: '',\n        category: '',\n        image_url: ''\n      })\n      setImagePreview('')\n    }\n    setImageFile(null)\n  }, [product, isOpen])\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setImageFile(file)\n      const reader = new FileReader()\n      reader.onloadend = () => {\n        setImagePreview(reader.result as string)\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  const uploadImage = async (): Promise<string> => {\n    if (!imageFile) return formData.image_url\n\n    setUploading(true)\n    try {\n      const uploadFormData = new FormData()\n      uploadFormData.append('file', imageFile)\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: uploadFormData,\n      })\n\n      const data = await response.json()\n      return data.url\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      return formData.image_url\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      // Upload image if there's a new one\n      const imageUrl = await uploadImage()\n\n      const productData = {\n        ...formData,\n        image_url: imageUrl,\n        price: parseFloat(formData.price),\n        stock_quantity: parseInt(formData.stock_quantity)\n      }\n\n      const url = product ? `/api/products/${product.id}` : '/api/products'\n      const method = product ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(productData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving product')\n      }\n    } catch (error) {\n      console.error('Error saving product:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {product ? 'Edit Product in List' : 'Add Product to List'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Image Upload */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Product Image\n            </label>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2\">\n                {imagePreview ? (\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Package className=\"h-12 w-12 text-gray-400\" />\n                )}\n              </div>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"hidden\"\n                id=\"image-upload\"\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Choose Image\n              </label>\n            </div>\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Net Weight */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Net Weight *\n            </label>\n            <input\n              type=\"text\"\n              required\n              placeholder=\"e.g., 100g, 1L, 250ml\"\n              value={formData.net_weight}\n              onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.price}\n              onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Stock Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Stock Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              required\n              value={formData.stock_quantity}\n              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select Category</option>\n              {PRODUCT_CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || uploading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading || uploading ? 'Saving...' : (product ? 'Update in List' : 'Add to List')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAqB;;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,YAAY;QACZ,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,YAAY,QAAQ,UAAU;oBAC9B,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,gBAAgB,QAAQ,cAAc,CAAC,QAAQ;oBAC/C,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS,IAAI;gBAClC;gBACA,gBAAgB,QAAQ,SAAS,IAAI;YACvC,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,YAAY;oBACZ,OAAO;oBACP,gBAAgB;oBAChB,UAAU;oBACV,WAAW;gBACb;gBACA,gBAAgB;YAClB;YACA,aAAa;QACf;iCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,aAAa;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,gBAAgB,OAAO,MAAM;YAC/B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,SAAS,SAAS;QAEzC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,SAAS,SAAS;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,oCAAoC;YACpC,MAAM,WAAW,MAAM;YAEvB,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,WAAW;gBACX,OAAO,WAAW,SAAS,KAAK;gBAChC,gBAAgB,SAAS,SAAS,cAAc;YAClD;YAEA,MAAM,MAAM,UAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,UAAU,QAAQ;YAEjC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,yBAAyB;;;;;;sCAEtC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,6BACC,6LAAC;gDACC,KAAK;gDACL,KAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;;;;;;sDAEL,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACvE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC3E,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU,WAAW;oCACrB,WAAU;8CAET,WAAW,YAAY,cAAe,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 4006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport ProductModal from './ProductModal'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data.products || [])\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this product?')) return\n\n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error)\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingProduct(null)\n    fetchProducts()\n    onStatsUpdate()\n  }\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === '' || product.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"flex space-x-4\">\n          <div className=\"relative\">\n            <Search\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n              }}\n            />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n              }}\n            />\n          </div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          >\n            <option value=\"\">All Categories</option>\n            {PRODUCT_CATEGORIES.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add to Product List\n        </button>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => (\n          <div\n            key={product.id}\n            className=\"rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n            }}\n          >\n            <div\n              className=\"aspect-square flex items-center justify-center transition-colors duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n              }}\n            >\n              {product.image_url ? (\n                <img\n                  src={product.image_url}\n                  alt={product.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <Package\n                  className=\"h-16 w-16 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                />\n              )}\n            </div>\n            <div className=\"p-4\">\n              <h3\n                className=\"font-semibold mb-1 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                {product.name}\n              </h3>\n              <p\n                className=\"text-sm mb-2 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                {product.category}\n              </p>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-lg font-bold text-green-600\">₱{product.price}</span>\n                <span\n                  className=\"text-sm transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                >\n                  {product.net_weight}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center mb-4\">\n                <span\n                  className={`text-sm ${product.stock_quantity < 10 ? 'text-red-600' : ''}`}\n                  style={{\n                    color: product.stock_quantity >= 10\n                      ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')\n                      : '#dc2626'\n                  }}\n                >\n                  Stock: {product.stock_quantity}\n                </span>\n                {product.stock_quantity < 10 && (\n                  <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">Low Stock</span>\n                )}\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleEdit(product)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\"\n                >\n                  <Edit className=\"h-4 w-4 mr-1\" />\n                  Edit\n                </button>\n                <button\n                  onClick={() => handleDelete(product.id)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-1\" />\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products in list</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm || selectedCategory\n              ? 'Try adjusting your search or filter criteria'\n              : 'Get started by adding your first product to the list'}\n          </p>\n        </div>\n      )}\n\n      {/* Product Modal */}\n      <ProductModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        product={editingProduct}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYe,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IAC7E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;QAClB;QACA;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,kBAAkB,qBAAqB,MAAM,QAAQ,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCACL,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;kDAEF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,YAAY;4CACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4CACzD,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;;;;;;;0CAGJ,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;kDAEA,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;;0CAEA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;0CAEC,QAAQ,SAAS,iBAChB,6LAAC;oCACC,KAAK,QAAQ,SAAS;oCACtB,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6LAAC,2MAAA,CAAA,UAAO;oCACN,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;;;;;;0CAIN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,QAAQ;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAmC;oDAAE,QAAQ,KAAK;;;;;;;0DAClE,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC,QAAQ,UAAU;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,QAAQ,cAAc,GAAG,KAAK,iBAAiB,IAAI;gDACzE,OAAO;oDACL,OAAO,QAAQ,cAAc,IAAI,KAC5B,kBAAkB,SAAS,YAAY,YACxC;gDACN;;oDACD;oDACS,QAAQ,cAAc;;;;;;;4CAE/B,QAAQ,cAAc,GAAG,oBACxB,6LAAC;gDAAK,WAAU;0DAAoD;;;;;;;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;;kEAEV,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;gDACtC,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAnFpC,QAAQ,EAAE;;;;;;;;;;YA4FpB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,mBACX,iDACA;;;;;;;;;;;;0BAMV,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB;GA1OwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 4454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtModal.tsx"], "sourcesContent": ["'use client'\n\nimport { X } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n\nimport { CustomerDebt } from '@/lib/supabase'\n\ninterface DebtModalProps {\n  isOpen: boolean\n  onClose: () => void\n  debt?: CustomerDebt | null\n}\n\nexport default function DebtModal({ isOpen, onClose, debt }: DebtModalProps) {\n  const [formData, setFormData] = useState({\n    customer_name: '',\n    customer_family_name: '',\n    product_name: '',\n    product_price: '',\n    quantity: '',\n    debt_date: ''\n  })\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (debt) {\n      setFormData({\n        customer_name: debt.customer_name,\n        customer_family_name: debt.customer_family_name,\n        product_name: debt.product_name,\n        product_price: debt.product_price.toString(),\n        quantity: debt.quantity.toString(),\n        debt_date: debt.debt_date\n      })\n    } else {\n      setFormData({\n        customer_name: '',\n        customer_family_name: '',\n        product_name: '',\n        product_price: '',\n        quantity: '',\n        debt_date: new Date().toISOString().split('T')[0] || ''\n      })\n    }\n  }, [debt, isOpen])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const debtData = {\n        ...formData,\n        product_price: parseFloat(formData.product_price),\n        quantity: parseInt(formData.quantity)\n      }\n\n      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'\n      const method = debt ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(debtData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving debt record')\n      }\n    } catch (error) {\n      console.error('Error saving debt record:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  const totalAmount = formData.product_price && formData.quantity \n    ? (parseFloat(formData.product_price) * parseInt(formData.quantity)).toFixed(2)\n    : '0.00'\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Customer Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer First Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_name}\n              onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Juan\"\n            />\n          </div>\n\n          {/* Customer Family Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer Family Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_family_name}\n              onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Dela Cruz\"\n            />\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.product_name}\n              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Lucky Me Pancit Canton\"\n            />\n          </div>\n\n          {/* Product Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.product_price}\n              onChange={(e) => setFormData({ ...formData, product_price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              required\n              value={formData.quantity}\n              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"1\"\n            />\n          </div>\n\n          {/* Debt Date */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Debt Date *\n            </label>\n            <input\n              type=\"date\"\n              required\n              value={formData.debt_date}\n              onChange={(e) => setFormData({ ...formData, debt_date: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Total Amount Display */}\n          <div className=\"bg-gray-50 p-3 rounded-md\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700\">Total Amount:</span>\n              <span className=\"text-lg font-bold text-green-600\">₱{totalAmount}</span>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Saving...' : (debt ? 'Update' : 'Add Record')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAkB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,eAAe,KAAK,aAAa;oBACjC,sBAAsB,KAAK,oBAAoB;oBAC/C,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa,CAAC,QAAQ;oBAC1C,UAAU,KAAK,QAAQ,CAAC,QAAQ;oBAChC,WAAW,KAAK,SAAS;gBAC3B;YACF,OAAO;gBACL,YAAY;oBACV,eAAe;oBACf,sBAAsB;oBACtB,cAAc;oBACd,eAAe;oBACf,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACvD;YACF;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,UAAU,SAAS,SAAS,QAAQ;YACtC;YAEA,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc,SAAS,aAAa,IAAI,SAAS,QAAQ,GAC3D,CAAC,WAAW,SAAS,aAAa,IAAI,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,KAC3E;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,OAAO,qBAAqB;;;;;;sCAE/B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,oBAAoB;oCACpC,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjF,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACtE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;;4CAAmC;4CAAE;;;;;;;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAe,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GAlNwB;KAAA", "debugId": null}}, {"offset": {"line": 4857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\nimport { format } from 'date-fns'\n\ninterface DebtsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [debts, setDebts] = useState<CustomerDebt[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)\n\n  useEffect(() => {\n    fetchDebts()\n  }, [])\n\n  const fetchDebts = async () => {\n    try {\n      const response = await fetch('/api/debts')\n      const data = await response.json()\n      setDebts(data.debts || [])\n    } catch (error) {\n      console.error('Error fetching debts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this debt record?')) return\n\n    try {\n      const response = await fetch(`/api/debts/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setDebts(debts.filter(d => d.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting debt:', error)\n    }\n  }\n\n  const handleEdit = (debt: CustomerDebt) => {\n    setEditingDebt(debt)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingDebt(null)\n    fetchDebts()\n    onStatsUpdate()\n  }\n\n  const filteredDebts = debts.filter(debt => {\n    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n    const productName = debt.product_name.toLowerCase()\n    const search = searchTerm.toLowerCase()\n    return customerName.includes(search) || productName.includes(search)\n  })\n\n  // Group debts by customer\n  const groupedDebts = filteredDebts.reduce((acc, debt) => {\n    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`\n    if (!acc[customerKey]) {\n      acc[customerKey] = []\n    }\n    acc[customerKey].push(debt)\n    return acc\n  }, {} as Record<string, CustomerDebt[]>)\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"relative\">\n          <Search\n            className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Search by customer or product...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          />\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Debt Record\n        </button>\n      </div>\n\n      {/* Debts List */}\n      <div className=\"space-y-6\">\n        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {\n          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)\n          \n          return (\n            <div key={customerName} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-5 w-5 text-gray-400 mr-2\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{customerName}</h3>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-600\">{customerDebts.length} item(s)</p>\n                    <p className=\"text-lg font-bold text-red-600\">₱{totalAmount.toFixed(2)}</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"divide-y divide-gray-200\">\n                {customerDebts.map((debt) => (\n                  <div key={debt.id} className=\"px-6 py-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-gray-900\">{debt.product_name}</h4>\n                        <div className=\"mt-1 text-sm text-gray-600 space-y-1\">\n                          <div className=\"flex items-center\">\n                            <span>Quantity: {debt.quantity}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">₱{debt.total_amount.toFixed(2)}</p>\n                        </div>\n                        <button\n                          onClick={() => handleEdit(debt)}\n                          className=\"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(debt.id)}\n                          className=\"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {filteredDebts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debt records found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm\n              ? 'Try adjusting your search criteria'\n              : 'Get started by adding your first debt record'}\n          </p>\n        </div>\n      )}\n\n      {/* Debt Modal */}\n      <DebtModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        debt={editingDebt}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AAae,SAAS,aAAa,EAAE,aAAa,EAAqB;;IACvE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sDAAsD;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;QACf;QACA;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;QACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;QACjD,MAAM,SAAS,WAAW,WAAW;QACrC,OAAO,aAAa,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC;IAC/D;IAEA,0BAA0B;IAC1B,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;QAC9C,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE;QACxE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACrB,GAAG,CAAC,YAAY,GAAG,EAAE;QACvB;QACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCACL,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;0CAEF,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;;;;;;;kCAGJ,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,cAAc,cAAc;oBAC9D,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;oBAEjF,qBACE,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAyB,cAAc,MAAM;wDAAC;;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;;wDAAiC;wDAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK1E,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAkB,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B,KAAK,YAAY;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAW,KAAK,QAAQ;;;;;;;sFAC9B,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;;gFAAK;gFAAc,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;;gFAAK;gFAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;oEAA8B;oEAAE,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAEzE,6LAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE;4DACnC,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA9BhB,KAAK,EAAE;;;;;;;;;;;uBAhBb;;;;;gBAuDd;;;;;;YAGD,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,aACG,uCACA;;;;;;;;;;;;0BAMV,6LAAC,kIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId;GAnMwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 5347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/FamilyGallery.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon } from 'lucide-react'\n\ninterface Photo {\n  id: string\n  url: string\n  title: string\n  description: string\n  date: string\n  likes: number\n  isLiked: boolean\n}\n\nexport default function FamilyGallery() {\n  const [photos, setPhotos] = useState<Photo[]>([\n    {\n      id: '1',\n      url: '/api/placeholder/400/300',\n      title: 'Family Store Opening',\n      description: 'Grand opening of our Revantad Store with the whole family',\n      date: '2024-01-15',\n      likes: 12,\n      isLiked: true,\n    },\n    {\n      id: '2',\n      url: '/api/placeholder/400/300',\n      title: 'Store Anniversary',\n      description: 'Celebrating our first year in business',\n      date: '2024-02-20',\n      likes: 8,\n      isLiked: false,\n    },\n    {\n      id: '3',\n      url: '/api/placeholder/400/300',\n      title: 'Community Event',\n      description: 'Participating in the local community festival',\n      date: '2024-03-10',\n      likes: 15,\n      isLiked: true,\n    },\n  ])\n\n  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)\n  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)\n  const [uploadForm, setUploadForm] = useState({\n    title: '',\n    description: '',\n    file: null as File | null,\n  })\n\n  const handleLike = (photoId: string) => {\n    setPhotos(photos.map(photo => \n      photo.id === photoId \n        ? { \n            ...photo, \n            likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1,\n            isLiked: !photo.isLiked \n          }\n        : photo\n    ))\n  }\n\n  const handleDelete = (photoId: string) => {\n    if (confirm('Are you sure you want to delete this photo?')) {\n      setPhotos(photos.filter(photo => photo.id !== photoId))\n    }\n  }\n\n  const handleUpload = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (uploadForm.file && uploadForm.title) {\n      const newPhoto: Photo = {\n        id: Date.now().toString(),\n        url: URL.createObjectURL(uploadForm.file),\n        title: uploadForm.title,\n        description: uploadForm.description,\n        date: new Date().toISOString().split('T')[0] || '',\n        likes: 0,\n        isLiked: false,\n      }\n      setPhotos([newPhoto, ...photos])\n      setUploadForm({ title: '', description: '', file: null })\n      setIsUploadModalOpen(false)\n    }\n  }\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setUploadForm({ ...uploadForm, file })\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Family Gallery</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Preserve your family memories and store moments\n          </p>\n        </div>\n        <button\n          onClick={() => setIsUploadModalOpen(true)}\n          className=\"btn-primary flex items-center\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Photo\n        </button>\n      </div>\n\n      {/* Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"card p-6 text-center\">\n          <ImageIcon className=\"h-8 w-8 text-green-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{photos.length}</p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Photos</p>\n        </div>\n        <div className=\"card p-6 text-center\">\n          <Heart className=\"h-8 w-8 text-red-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {photos.reduce((sum, photo) => sum + photo.likes, 0)}\n          </p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Likes</p>\n        </div>\n        <div className=\"card p-6 text-center\">\n          <Upload className=\"h-8 w-8 text-blue-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {new Date().getFullYear()}\n          </p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Year Started</p>\n        </div>\n      </div>\n\n      {/* Photo Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {photos.map((photo) => (\n          <div key={photo.id} className=\"card overflow-hidden group\">\n            <div className=\"relative aspect-video bg-gray-200 dark:bg-gray-700\">\n              <div className=\"w-full h-full bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 flex items-center justify-center\">\n                <ImageIcon className=\"h-16 w-16 text-gray-400\" />\n              </div>\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100\">\n                <button\n                  onClick={() => setSelectedPhoto(photo)}\n                  className=\"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium\"\n                >\n                  View Details\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"p-4\">\n              <h3 className=\"font-semibold text-gray-900 dark:text-white mb-1\">\n                {photo.title}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\">\n                {photo.description}\n              </p>\n              \n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {new Date(photo.date).toLocaleDateString()}\n                </span>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => handleLike(photo.id)}\n                    className={`flex items-center space-x-1 text-sm ${\n                      photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'\n                    }`}\n                  >\n                    <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />\n                    <span>{photo.likes}</span>\n                  </button>\n                  \n                  <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500\">\n                    <Share2 className=\"h-4 w-4\" />\n                  </button>\n                  \n                  <button \n                    onClick={() => handleDelete(photo.id)}\n                    className=\"text-gray-500 dark:text-gray-400 hover:text-red-500\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Upload Modal */}\n      {isUploadModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Add New Photo\n            </h3>\n            \n            <form onSubmit={handleUpload} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Photo File\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleFileChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Title\n                </label>\n                <input\n                  type=\"text\"\n                  value={uploadForm.title}\n                  onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  value={uploadForm.description}\n                  onChange={(e) => setUploadForm({ ...uploadForm, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                />\n              </div>\n              \n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsUploadModalOpen(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 btn-primary\"\n                >\n                  Upload Photo\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Photo Detail Modal */}\n      {selectedPhoto && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-2xl\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {selectedPhoto.title}\n              </h3>\n              <button\n                onClick={() => setSelectedPhoto(null)}\n                className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                ✕\n              </button>\n            </div>\n            \n            <div className=\"aspect-video bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center\">\n              <ImageIcon className=\"h-24 w-24 text-gray-400\" />\n            </div>\n            \n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              {selectedPhoto.description}\n            </p>\n            \n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(selectedPhoto.date).toLocaleDateString()}\n              </span>\n              \n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => handleLike(selectedPhoto.id)}\n                  className={`flex items-center space-x-1 ${\n                    selectedPhoto.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'\n                  }`}\n                >\n                  <Heart className={`h-5 w-5 ${selectedPhoto.isLiked ? 'fill-current' : ''}`} />\n                  <span>{selectedPhoto.likes}</span>\n                </button>\n                \n                <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500\">\n                  <Download className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAee,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,aAAa;QACb,MAAM;IACR;IAEA,MAAM,aAAa,CAAC;QAClB,UAAU,OAAO,GAAG,CAAC,CAAA,QACnB,MAAM,EAAE,KAAK,UACT;gBACE,GAAG,KAAK;gBACR,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG;gBACvD,SAAS,CAAC,MAAM,OAAO;YACzB,IACA;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,gDAAgD;YAC1D,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI,WAAW,KAAK,EAAE;YACvC,MAAM,WAAkB;gBACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,KAAK,IAAI,eAAe,CAAC,WAAW,IAAI;gBACxC,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,WAAW;gBACnC,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBAChD,OAAO;gBACP,SAAS;YACX;YACA,UAAU;gBAAC;mBAAa;aAAO;YAC/B,cAAc;gBAAE,OAAO;gBAAI,aAAa;gBAAI,MAAM;YAAK;YACvD,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,cAAc;gBAAE,GAAG,UAAU;gBAAE;YAAK;QACtC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAE,WAAU;0CAAoD,OAAO,MAAM;;;;;;0CAC9E,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CACV,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;;;;;;0CAEpD,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CACV,IAAI,OAAO,WAAW;;;;;;0CAEzB,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAEd,6LAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,WAAW,MAAM,EAAE;wDAClC,WAAW,CAAC,oCAAoC,EAC9C,MAAM,OAAO,GAAG,iBAAiB,oCACjC;;0EAEF,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI;;;;;;0EAClE,6LAAC;0EAAM,MAAM,KAAK;;;;;;;;;;;;kEAGpB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAGpB,6LAAC;wDACC,SAAS,IAAM,aAAa,MAAM,EAAE;wDACpC,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA/ClB,MAAM,EAAE;;;;;;;;;;YAyDrB,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,WAAW,KAAK;4CACvB,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,WAAW,WAAW;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5E,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,+BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,cAAc,KAAK;;;;;;8CAEtB,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAE,WAAU;sCACV,cAAc,WAAW;;;;;;sCAG5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,IAAI,KAAK,cAAc,IAAI,EAAE,kBAAkB;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,WAAW,cAAc,EAAE;4CAC1C,WAAW,CAAC,4BAA4B,EACtC,cAAc,OAAO,GAAG,iBAAiB,oCACzC;;8DAEF,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,OAAO,GAAG,iBAAiB,IAAI;;;;;;8DAC1E,6LAAC;8DAAM,cAAc,KAAK;;;;;;;;;;;;sDAG5B,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GA5SwB;KAAA", "debugId": null}}, {"offset": {"line": 6061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Calendar.tsx"], "sourcesContent": ["'use client'\n\nimport { ChevronLeft, ChevronRight, Plus, Clock, MapPin, Users, Calendar as CalendarIcon, Moon } from 'lucide-react'\nimport { useState } from 'react'\n\n// Moon Phase Types and Interfaces\ninterface MoonPhase {\n  name: string\n  emoji: string\n  icon: string\n  illumination: number\n  description: string\n}\n\ninterface MoonPhaseData {\n  phase: MoonPhase\n  age: number\n  illumination: number\n  nextFullMoon: Date\n  nextNewMoon: Date\n}\n\ninterface Event {\n  id: string\n  title: string\n  description: string\n  date: string\n  time: string\n  type: 'delivery' | 'meeting' | 'reminder' | 'holiday' | 'personal'\n  location?: string\n  attendees?: string[]\n}\n\n// Bisaya-Tagalog Language Dictionary\nconst BISAYA_TAGALOG_TEXTS = {\n  // Calendar Terms\n  calendar: 'Kalendaryo',\n  today: 'Karon nga Adlaw',\n  events: 'Mga Panghitabo',\n  schedule: 'Iskedyul',\n\n  // Moon Phase Terms\n  moonPhases: 'Mga Hugis sa Bulan',\n  moonPhase: 'Hugis sa Bulan',\n  newMoon: 'Bag-ong Bulan',\n  waxingCrescent: 'Nagdako nga Sungay',\n  firstQuarter: 'Una nga Bahin',\n  waxingGibbous: 'Nagdako nga Bula',\n  fullMoon: 'Puno nga Bulan',\n  waningGibbous: 'Nagliit nga Bula',\n  lastQuarter: 'Katapusan nga Bahin',\n  waningCrescent: 'Nagliit nga Sungay',\n\n  // Moon Phase Descriptions\n  newMoonDesc: 'Ang bulan dili makita gikan sa yuta',\n  waxingCrescentDesc: 'Nipis nga sungay sa bulan sa tuo nga bahin',\n  firstQuarterDesc: 'Katunga sa bulan nag-hayag sa tuo nga bahin',\n  waxingGibbousDesc: 'Sobra sa katunga sa bulan nag-hayag',\n  fullMoonDesc: 'Tibuok nga bulan nag-hayag ug makita',\n  waningGibbousDesc: 'Sobra sa katunga nag-hayag, nagliit na',\n  lastQuarterDesc: 'Katunga sa bulan nag-hayag sa wala nga bahin',\n  waningCrescentDesc: 'Nipis nga sungay sa bulan sa wala nga bahin',\n\n  // UI Elements\n  addEvent: 'Dugang Event',\n  manage: 'Pagdumala',\n  upcoming: 'Umaabot na',\n  legend: 'Giya',\n  age: 'Edad',\n  illumination: 'Kahayag',\n  days: 'mga adlaw',\n  next: 'Sunod',\n  cancel: 'Kanselar',\n  description: 'Deskripsyon',\n  location: 'Lugar',\n  time: 'Oras',\n  date: 'Petsa',\n  title: 'Titulo',\n  type: 'Klase',\n  attendees: 'Mga Apil',\n  more: 'pa',\n\n  // Event Types\n  delivery: 'Delivery',\n  meeting: 'Meeting',\n  reminder: 'Pahinumdom',\n  holiday: 'Holiday',\n  personal: 'Personal'\n}\n\n// Moon Phase Calculation Functions\nconst getMoonPhases = (): MoonPhase[] => [\n  {\n    name: BISAYA_TAGALOG_TEXTS.newMoon,\n    emoji: '🌑',\n    icon: 'new-moon',\n    illumination: 0,\n    description: BISAYA_TAGALOG_TEXTS.newMoonDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waxingCrescent,\n    emoji: '🌒',\n    icon: 'waxing-crescent',\n    illumination: 25,\n    description: BISAYA_TAGALOG_TEXTS.waxingCrescentDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.firstQuarter,\n    emoji: '🌓',\n    icon: 'first-quarter',\n    illumination: 50,\n    description: BISAYA_TAGALOG_TEXTS.firstQuarterDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waxingGibbous,\n    emoji: '🌔',\n    icon: 'waxing-gibbous',\n    illumination: 75,\n    description: BISAYA_TAGALOG_TEXTS.waxingGibbousDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.fullMoon,\n    emoji: '🌕',\n    icon: 'full-moon',\n    illumination: 100,\n    description: BISAYA_TAGALOG_TEXTS.fullMoonDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waningGibbous,\n    emoji: '🌖',\n    icon: 'waning-gibbous',\n    illumination: 75,\n    description: BISAYA_TAGALOG_TEXTS.waningGibbousDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.lastQuarter,\n    emoji: '🌗',\n    icon: 'last-quarter',\n    illumination: 50,\n    description: BISAYA_TAGALOG_TEXTS.lastQuarterDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waningCrescent,\n    emoji: '🌘',\n    icon: 'waning-crescent',\n    illumination: 25,\n    description: BISAYA_TAGALOG_TEXTS.waningCrescentDesc\n  }\n]\n\nconst calculateMoonPhase = (date: Date): MoonPhaseData => {\n  // Known new moon date: January 6, 2000, 18:14 UTC\n  const knownNewMoon = new Date(2000, 0, 6, 18, 14)\n  const lunarCycle = 29.53058867 // Average lunar cycle in days\n\n  // Calculate days since known new moon\n  const daysSinceNewMoon = (date.getTime() - knownNewMoon.getTime()) / (1000 * 60 * 60 * 24)\n\n  // Calculate current position in lunar cycle\n  const cyclePosition = daysSinceNewMoon % lunarCycle\n  const moonAge = cyclePosition < 0 ? cyclePosition + lunarCycle : cyclePosition\n\n  // Calculate illumination percentage\n  const illumination = Math.round((1 - Math.cos((moonAge / lunarCycle) * 2 * Math.PI)) * 50)\n\n  // Determine moon phase based on age\n  const phases = getMoonPhases()\n  let phaseIndex = 0\n\n  if (moonAge < 1.84566) phaseIndex = 0      // New Moon\n  else if (moonAge < 5.53699) phaseIndex = 1 // Waxing Crescent\n  else if (moonAge < 9.22831) phaseIndex = 2 // First Quarter\n  else if (moonAge < 12.91963) phaseIndex = 3 // Waxing Gibbous\n  else if (moonAge < 16.61096) phaseIndex = 4 // Full Moon\n  else if (moonAge < 20.30228) phaseIndex = 5 // Waning Gibbous\n  else if (moonAge < 23.99361) phaseIndex = 6 // Last Quarter\n  else phaseIndex = 7                        // Waning Crescent\n\n  // Calculate next full moon and new moon dates\n  const daysToNextFullMoon = (14.76529 - moonAge + lunarCycle) % lunarCycle\n  const daysToNextNewMoon = (lunarCycle - moonAge) % lunarCycle\n\n  const nextFullMoon = new Date(date.getTime() + daysToNextFullMoon * 24 * 60 * 60 * 1000)\n  const nextNewMoon = new Date(date.getTime() + daysToNextNewMoon * 24 * 60 * 60 * 1000)\n\n  return {\n    phase: phases[phaseIndex] ?? phases[0],\n    age: Math.round(moonAge * 10) / 10,\n    illumination,\n    nextFullMoon,\n    nextNewMoon\n  }\n}\n\n// Moon Phase Icon Component\nconst MoonPhaseIcon = ({ phase, size = 16, className = \"\" }: { phase: MoonPhase, size?: number, className?: string }) => {\n  const iconStyle = {\n    width: size,\n    height: size,\n    fontSize: size,\n    display: 'inline-block',\n    lineHeight: 1\n  }\n\n  return (\n    <span\n      className={`moon-phase-icon ${className}`}\n      style={iconStyle}\n      title={`${phase.name} - ${phase.description}`}\n    >\n      {phase.emoji}\n    </span>\n  )\n}\n\n// Moon Phase Tooltip Component\nconst MoonPhaseTooltip = ({ moonData, className = \"\" }: { moonData: MoonPhaseData, className?: string }) => {\n  return (\n    <div className={`absolute z-10 p-3 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 min-w-48 ${className}`}>\n      <div className=\"flex items-center space-x-2 mb-2\">\n        <MoonPhaseIcon phase={moonData.phase} size={20} />\n        <span className=\"font-semibold text-gray-900 dark:text-white\">{moonData.phase.name}</span>\n      </div>\n      <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n        <p>{moonData.phase.description}</p>\n        <p>{BISAYA_TAGALOG_TEXTS.age}: {moonData.age} {BISAYA_TAGALOG_TEXTS.days}</p>\n        <p>{BISAYA_TAGALOG_TEXTS.illumination}: {moonData.illumination}%</p>\n        <p className=\"text-xs pt-1 border-t border-gray-200 dark:border-gray-600\">\n          {BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}: {moonData.nextFullMoon.toLocaleDateString('tl-PH')}\n        </p>\n      </div>\n    </div>\n  )\n}\n\nexport default function Calendar() {\n  const [currentDate, setCurrentDate] = useState(new Date())\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null)\n  const [isEventModalOpen, setIsEventModalOpen] = useState(false)\n  const [showMoonPhases, setShowMoonPhases] = useState(true)\n  const [hoveredMoonPhase, setHoveredMoonPhase] = useState<{ date: Date, moonData: MoonPhaseData, position: { x: number, y: number } } | null>(null)\n  const [events, setEvents] = useState<Event[]>([\n    {\n      id: '1',\n      title: 'Supplier Delivery',\n      description: 'Weekly grocery delivery from main supplier',\n      date: '2024-01-22',\n      time: '09:00',\n      type: 'delivery',\n      location: 'Store Front',\n    },\n    {\n      id: '2',\n      title: 'Monthly Inventory Check',\n      description: 'Complete inventory count and stock verification',\n      date: '2024-01-25',\n      time: '14:00',\n      type: 'reminder',\n    },\n    {\n      id: '3',\n      title: 'Community Meeting',\n      description: 'Barangay business owners meeting',\n      date: '2024-01-28',\n      time: '16:00',\n      type: 'meeting',\n      location: 'Barangay Hall',\n      attendees: ['Maria Santos', 'Juan Dela Cruz', 'Ana Reyes'],\n    },\n    {\n      id: '4',\n      title: 'New Year Holiday',\n      description: 'Store closed for New Year celebration',\n      date: '2024-01-01',\n      time: '00:00',\n      type: 'holiday',\n    },\n  ])\n\n  const [newEvent, setNewEvent] = useState({\n    title: '',\n    description: '',\n    date: '',\n    time: '',\n    type: 'reminder' as Event['type'],\n    location: '',\n  })\n\n  const monthNames = [\n    'Enero', 'Pebrero', 'Marso', 'Abril', 'Mayo', 'Hunyo',\n    'Hulyo', 'Agosto', 'Septyembre', 'Oktubre', 'Nobyembre', 'Disyembre'\n  ]\n\n  const daysOfWeek = ['Dom', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab']\n\n  const getDaysInMonth = (date: Date) => {\n    const year = date.getFullYear()\n    const month = date.getMonth()\n    const firstDay = new Date(year, month, 1)\n    const lastDay = new Date(year, month + 1, 0)\n    const daysInMonth = lastDay.getDate()\n    const startingDayOfWeek = firstDay.getDay()\n\n    const days = []\n    \n    // Add empty cells for days before the first day of the month\n    for (let i = 0; i < startingDayOfWeek; i++) {\n      days.push(null)\n    }\n    \n    // Add days of the month\n    for (let day = 1; day <= daysInMonth; day++) {\n      days.push(new Date(year, month, day))\n    }\n    \n    return days\n  }\n\n  const getEventsForDate = (date: Date) => {\n    const dateString = date.toISOString().split('T')[0]\n    return events.filter(event => event.date === dateString)\n  }\n\n  const getEventTypeColor = (type: Event['type']) => {\n    switch (type) {\n      case 'delivery':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n      case 'meeting':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'\n      case 'reminder':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n      case 'holiday':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n      case 'personal':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'\n    }\n  }\n\n  const navigateMonth = (direction: 'prev' | 'next') => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev)\n      if (direction === 'prev') {\n        newDate.setMonth(prev.getMonth() - 1)\n      } else {\n        newDate.setMonth(prev.getMonth() + 1)\n      }\n      return newDate\n    })\n  }\n\n  const handleAddEvent = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (newEvent.title && newEvent.date && newEvent.time) {\n      const event: Event = {\n        id: Date.now().toString(),\n        ...newEvent,\n      }\n      setEvents([...events, event])\n      setNewEvent({\n        title: '',\n        description: '',\n        date: '',\n        time: '',\n        type: 'reminder',\n        location: '',\n      })\n      setIsEventModalOpen(false)\n    }\n  }\n\n  const days = getDaysInMonth(currentDate)\n  const today = new Date()\n\n  return (\n    <div className=\"space-y-6 bisaya-calendar\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white cultural-accent bisaya-text\">{BISAYA_TAGALOG_TEXTS.calendar}</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1 bisaya-text\">\n            {BISAYA_TAGALOG_TEXTS.manage} sa inyong store {BISAYA_TAGALOG_TEXTS.events} ug {BISAYA_TAGALOG_TEXTS.schedule} uban sa lunar phases\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => setShowMoonPhases(!showMoonPhases)}\n            className={`flex items-center px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-105 ${\n              showMoonPhases\n                ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-400 shadow-md'\n                : 'border-gray-300 text-gray-600 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-slate-700'\n            }`}\n          >\n            <Moon className=\"h-4 w-4 mr-2\" />\n            {BISAYA_TAGALOG_TEXTS.moonPhases}\n          </button>\n          <button\n            onClick={() => setIsEventModalOpen(true)}\n            className=\"btn-primary flex items-center hover:scale-105 shadow-lg\"\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            {BISAYA_TAGALOG_TEXTS.addEvent}\n          </button>\n        </div>\n      </div>\n\n      {/* Calendar Navigation */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => navigateMonth('prev')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => setCurrentDate(new Date())}\n              className=\"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 shadow-sm\"\n            >\n              {BISAYA_TAGALOG_TEXTS.today}\n            </button>\n            <button\n              onClick={() => navigateMonth('next')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Calendar Grid */}\n        <div className=\"grid grid-cols-7 gap-1\">\n          {/* Day headers */}\n          {daysOfWeek.map(day => (\n            <div key={day} className=\"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400\">\n              {day}\n            </div>\n          ))}\n          \n          {/* Calendar days */}\n          {days.map((day, index) => {\n            if (!day) {\n              return <div key={index} className=\"p-3 h-28\"></div>\n            }\n\n            const dayEvents = getEventsForDate(day)\n            const isToday = day.toDateString() === today.toDateString()\n            const isSelected = selectedDate?.toDateString() === day.toDateString()\n            const moonData = calculateMoonPhase(day)\n\n            return (\n              <div\n                key={index}\n                onClick={() => setSelectedDate(day)}\n                className={`calendar-day-cell p-2 h-28 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 relative ${\n                  isToday ? 'bg-green-50 dark:bg-green-900/20 ring-1 ring-green-300 dark:ring-green-600' : ''\n                } ${isSelected ? 'ring-2 ring-green-500 bg-green-100 dark:bg-green-900/30' : ''}`}\n              >\n                {/* Day number and moon phase */}\n                <div className=\"flex items-center justify-between mb-1\">\n                  <div className={`text-sm font-medium ${\n                    isToday ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'\n                  }`}>\n                    {day.getDate()}\n                  </div>\n                  {showMoonPhases && (\n                    <div\n                      className=\"moon-phase-container relative\"\n                      onMouseEnter={(e) => {\n                        const rect = e.currentTarget.getBoundingClientRect()\n                        setHoveredMoonPhase({\n                          date: day,\n                          moonData,\n                          position: { x: rect.left, y: rect.top }\n                        })\n                      }}\n                      onMouseLeave={() => setHoveredMoonPhase(null)}\n                    >\n                      <MoonPhaseIcon\n                        phase={moonData.phase}\n                        size={14}\n                        className=\"opacity-80 hover:opacity-100 transition-opacity duration-200\"\n                      />\n                    </div>\n                  )}\n                </div>\n\n                {/* Events */}\n                <div className=\"space-y-1\">\n                  {dayEvents.slice(0, showMoonPhases ? 1 : 2).map(event => (\n                    <div\n                      key={event.id}\n                      className={`text-xs px-1 py-0.5 rounded truncate ${getEventTypeColor(event.type)}`}\n                    >\n                      {event.title}\n                    </div>\n                  ))}\n                  {dayEvents.length > (showMoonPhases ? 1 : 2) && (\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      +{dayEvents.length - (showMoonPhases ? 1 : 2)} {BISAYA_TAGALOG_TEXTS.more}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Moon Phase Information Panel */}\n      {showMoonPhases && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Current Moon Phase */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <Moon className=\"h-5 w-5 mr-2 text-blue-500\" />\n              {BISAYA_TAGALOG_TEXTS.moonPhase} Karon nga Adlaw\n            </h3>\n            {(() => {\n              const todayMoon = calculateMoonPhase(today)\n              return (\n                <div className=\"text-center\">\n                  <div className=\"mb-3\">\n                    <MoonPhaseIcon phase={todayMoon.phase} size={48} className=\"mx-auto\" />\n                  </div>\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">{todayMoon.phase.name}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{todayMoon.phase.description}</p>\n                  <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                    <div className=\"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                      <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.age}</div>\n                      <div className=\"text-gray-600 dark:text-gray-400\">{todayMoon.age} {BISAYA_TAGALOG_TEXTS.days}</div>\n                    </div>\n                    <div className=\"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                      <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.illumination}</div>\n                      <div className=\"text-gray-600 dark:text-gray-400\">{todayMoon.illumination}%</div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })()}\n          </div>\n\n          {/* Upcoming Moon Events */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <span className=\"text-lg mr-2\">🌙</span>\n              {BISAYA_TAGALOG_TEXTS.upcoming} nga Bulan {BISAYA_TAGALOG_TEXTS.events}\n            </h3>\n            {(() => {\n              const todayMoon = calculateMoonPhase(today)\n              return (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-xl\">🌕</span>\n                      <div>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {todayMoon.nextFullMoon.toLocaleDateString('tl-PH', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-xl\">🌑</span>\n                      <div>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.newMoon}</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {todayMoon.nextNewMoon.toLocaleDateString('tl-PH', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })()}\n          </div>\n\n          {/* Moon Phase Legend */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <span className=\"text-lg mr-2\">📖</span>\n              {BISAYA_TAGALOG_TEXTS.legend} sa {BISAYA_TAGALOG_TEXTS.moonPhases}\n            </h3>\n            <div className=\"grid grid-cols-1 gap-2\">\n              {getMoonPhases().map((phase, index) => (\n                <div key={index} className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:scale-102\">\n                  <MoonPhaseIcon phase={phase} size={18} />\n                  <div className=\"flex-1\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">{phase.name}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{phase.illumination}% {BISAYA_TAGALOG_TEXTS.illumination}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Moon Phase Tooltip */}\n      {hoveredMoonPhase && (\n        <div\n          className=\"fixed pointer-events-none z-50\"\n          style={{\n            left: hoveredMoonPhase.position.x,\n            top: hoveredMoonPhase.position.y - 10,\n            transform: 'translateY(-100%)'\n          }}\n        >\n          <MoonPhaseTooltip moonData={hoveredMoonPhase.moonData} />\n        </div>\n      )}\n\n      {/* Upcoming Events */}\n      <div className=\"card p-6 hover:shadow-lg transition-all duration-300\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <span className=\"text-lg mr-2\">📅</span>\n          {BISAYA_TAGALOG_TEXTS.upcoming} nga {BISAYA_TAGALOG_TEXTS.events}\n        </h3>\n        \n        <div className=\"space-y-3\">\n          {events\n            .filter(event => new Date(event.date) >= today)\n            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n            .slice(0, 5)\n            .map(event => (\n              <div key={event.id} className=\"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700\">\n                <div className={`p-2 rounded-lg ${getEventTypeColor(event.type)}`}>\n                  <CalendarIcon className=\"h-4 w-4\" />\n                </div>\n                \n                <div className=\"flex-1\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{event.title}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{event.description}</p>\n                  \n                  <div className=\"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-3 w-3\" />\n                      <span>{new Date(event.date).toLocaleDateString()} at {event.time}</span>\n                    </div>\n                    \n                    {event.location && (\n                      <div className=\"flex items-center space-x-1\">\n                        <MapPin className=\"h-3 w-3\" />\n                        <span>{event.location}</span>\n                      </div>\n                    )}\n                    \n                    {event.attendees && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Users className=\"h-3 w-3\" />\n                        <span>{event.attendees.length} {BISAYA_TAGALOG_TEXTS.attendees}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n        </div>\n      </div>\n\n      {/* Add Event Modal */}\n      {isEventModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md shadow-2xl animate-fade-in-up\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n              <Plus className=\"h-5 w-5 mr-2 text-green-500\" />\n              Dugang Bag-ong {BISAYA_TAGALOG_TEXTS.events}\n            </h3>\n            \n            <form onSubmit={handleAddEvent} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.title} sa {BISAYA_TAGALOG_TEXTS.events}\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.title}\n                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"I-type ang titulo sa event...\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.description}\n                </label>\n                <textarea\n                  value={newEvent.description}\n                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"Detalye sa event...\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {BISAYA_TAGALOG_TEXTS.date}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={newEvent.date}\n                    onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {BISAYA_TAGALOG_TEXTS.time}\n                  </label>\n                  <input\n                    type=\"time\"\n                    value={newEvent.time}\n                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.type} sa {BISAYA_TAGALOG_TEXTS.events}\n                </label>\n                <select\n                  value={newEvent.type}\n                  onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as Event['type'] })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                >\n                  <option value=\"reminder\">{BISAYA_TAGALOG_TEXTS.reminder}</option>\n                  <option value=\"delivery\">{BISAYA_TAGALOG_TEXTS.delivery}</option>\n                  <option value=\"meeting\">{BISAYA_TAGALOG_TEXTS.meeting}</option>\n                  <option value=\"holiday\">{BISAYA_TAGALOG_TEXTS.holiday}</option>\n                  <option value=\"personal\">{BISAYA_TAGALOG_TEXTS.personal}</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.location} (Optional)\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.location}\n                  onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"Asa ang event...\"\n                />\n              </div>\n              \n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsEventModalOpen(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 hover:scale-105\"\n                >\n                  {BISAYA_TAGALOG_TEXTS.cancel}\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 btn-primary hover:scale-105 shadow-lg\"\n                >\n                  {BISAYA_TAGALOG_TEXTS.addEvent}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAiCA,qCAAqC;AACrC,MAAM,uBAAuB;IAC3B,iBAAiB;IACjB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,UAAU;IAEV,mBAAmB;IACnB,YAAY;IACZ,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,UAAU;IACV,eAAe;IACf,aAAa;IACb,gBAAgB;IAEhB,0BAA0B;IAC1B,aAAa;IACb,oBAAoB;IACpB,kBAAkB;IAClB,mBAAmB;IACnB,cAAc;IACd,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IAEpB,cAAc;IACd,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,KAAK;IACL,cAAc;IACd,MAAM;IACN,MAAM;IACN,QAAQ;IACR,aAAa;IACb,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,WAAW;IACX,MAAM;IAEN,cAAc;IACd,UAAU;IACV,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;AACZ;AAEA,mCAAmC;AACnC,MAAM,gBAAgB,IAAmB;QACvC;YACE,MAAM,qBAAqB,OAAO;YAClC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,WAAW;QAC/C;QACA;YACE,MAAM,qBAAqB,cAAc;YACzC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,kBAAkB;QACtD;QACA;YACE,MAAM,qBAAqB,YAAY;YACvC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,gBAAgB;QACpD;QACA;YACE,MAAM,qBAAqB,aAAa;YACxC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,iBAAiB;QACrD;QACA;YACE,MAAM,qBAAqB,QAAQ;YACnC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,YAAY;QAChD;QACA;YACE,MAAM,qBAAqB,aAAa;YACxC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,iBAAiB;QACrD;QACA;YACE,MAAM,qBAAqB,WAAW;YACtC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,eAAe;QACnD;QACA;YACE,MAAM,qBAAqB,cAAc;YACzC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,kBAAkB;QACtD;KACD;AAED,MAAM,qBAAqB,CAAC;IAC1B,kDAAkD;IAClD,MAAM,eAAe,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI;IAC9C,MAAM,aAAa,YAAY,8BAA8B;;IAE7D,sCAAsC;IACtC,MAAM,mBAAmB,CAAC,KAAK,OAAO,KAAK,aAAa,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAEzF,4CAA4C;IAC5C,MAAM,gBAAgB,mBAAmB;IACzC,MAAM,UAAU,gBAAgB,IAAI,gBAAgB,aAAa;IAEjE,oCAAoC;IACpC,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,AAAC,UAAU,aAAc,IAAI,KAAK,EAAE,CAAC,IAAI;IAEvF,oCAAoC;IACpC,MAAM,SAAS;IACf,IAAI,aAAa;IAEjB,IAAI,UAAU,SAAS,aAAa,EAAO,WAAW;;SACjD,IAAI,UAAU,SAAS,aAAa,EAAE,kBAAkB;;SACxD,IAAI,UAAU,SAAS,aAAa,EAAE,gBAAgB;;SACtD,IAAI,UAAU,UAAU,aAAa,EAAE,iBAAiB;;SACxD,IAAI,UAAU,UAAU,aAAa,EAAE,YAAY;;SACnD,IAAI,UAAU,UAAU,aAAa,EAAE,iBAAiB;;SACxD,IAAI,UAAU,UAAU,aAAa,EAAE,eAAe;;SACtD,aAAa,EAAyB,kBAAkB;;IAE7D,8CAA8C;IAC9C,MAAM,qBAAqB,CAAC,WAAW,UAAU,UAAU,IAAI;IAC/D,MAAM,oBAAoB,CAAC,aAAa,OAAO,IAAI;IAEnD,MAAM,eAAe,IAAI,KAAK,KAAK,OAAO,KAAK,qBAAqB,KAAK,KAAK,KAAK;IACnF,MAAM,cAAc,IAAI,KAAK,KAAK,OAAO,KAAK,oBAAoB,KAAK,KAAK,KAAK;IAEjF,OAAO;QACL,OAAO,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;QACtC,KAAK,KAAK,KAAK,CAAC,UAAU,MAAM;QAChC;QACA;QACA;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,EAA2D;IAClH,MAAM,YAAY;QAChB,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,YAAY;IACd;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,gBAAgB,EAAE,WAAW;QACzC,OAAO;QACP,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,WAAW,EAAE;kBAE5C,MAAM,KAAK;;;;;;AAGlB;KAlBM;AAoBN,+BAA+B;AAC/B,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmD;IACrG,qBACE,6LAAC;QAAI,WAAW,CAAC,uHAAuH,EAAE,WAAW;;0BACnJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAc,OAAO,SAAS,KAAK;wBAAE,MAAM;;;;;;kCAC5C,6LAAC;wBAAK,WAAU;kCAA+C,SAAS,KAAK,CAAC,IAAI;;;;;;;;;;;;0BAEpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG,SAAS,KAAK,CAAC,WAAW;;;;;;kCAC9B,6LAAC;;4BAAG,qBAAqB,GAAG;4BAAC;4BAAG,SAAS,GAAG;4BAAC;4BAAE,qBAAqB,IAAI;;;;;;;kCACxE,6LAAC;;4BAAG,qBAAqB,YAAY;4BAAC;4BAAG,SAAS,YAAY;4BAAC;;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;;4BACV,qBAAqB,IAAI;4BAAC;4BAAE,qBAAqB,QAAQ;4BAAC;4BAAG,SAAS,YAAY,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;AAKjH;MAjBM;AAmBS,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsF;IAC7I,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;YACV,WAAW;gBAAC;gBAAgB;gBAAkB;aAAY;QAC5D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB;QAAS;QAAW;QAAS;QAAS;QAAQ;QAC9C;QAAS;QAAU;QAAc;QAAW;QAAa;KAC1D;IAED,MAAM,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ;QAC3B,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO;QACvC,MAAM,UAAU,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC1C,MAAM,cAAc,QAAQ,OAAO;QACnC,MAAM,oBAAoB,SAAS,MAAM;QAEzC,MAAM,OAAO,EAAE;QAEf,6DAA6D;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YAC1C,KAAK,IAAI,CAAC;QACZ;QAEA,wBAAwB;QACxB,IAAK,IAAI,MAAM,GAAG,OAAO,aAAa,MAAO;YAC3C,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,OAAO;QAClC;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IAC/C;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,IAAI,cAAc,QAAQ;gBACxB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC,OAAO;gBACL,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,EAAE;YACpD,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,QAAQ;YACb;YACA,UAAU;mBAAI;gBAAQ;aAAM;YAC5B,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,oBAAoB;QACtB;IACF;IAEA,MAAM,OAAO,eAAe;IAC5B,MAAM,QAAQ,IAAI;IAElB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgF,qBAAqB,QAAQ;;;;;;0CAC3H,6LAAC;gCAAE,WAAU;;oCACV,qBAAqB,MAAM;oCAAC;oCAAkB,qBAAqB,MAAM;oCAAC;oCAAK,qBAAqB,QAAQ;oCAAC;;;;;;;;;;;;;kCAGlH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAW,CAAC,0FAA0F,EACpG,iBACI,mHACA,kHACJ;;kDAEF,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,UAAU;;;;;;;0CAElC,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,UAAU,CAAC,YAAY,QAAQ,GAAG;oCAAC;oCAAE,YAAY,WAAW;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCACC,SAAS,IAAM,eAAe,IAAI;wCAClC,WAAU;kDAET,qBAAqB,KAAK;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,6LAAC;wBAAI,WAAU;;4BAEZ,WAAW,GAAG,CAAC,CAAA,oBACd,6LAAC;oCAAc,WAAU;8CACtB;mCADO;;;;;4BAMX,KAAK,GAAG,CAAC,CAAC,KAAK;gCACd,IAAI,CAAC,KAAK;oCACR,qBAAO,6LAAC;wCAAgB,WAAU;uCAAjB;;;;;gCACnB;gCAEA,MAAM,YAAY,iBAAiB;gCACnC,MAAM,UAAU,IAAI,YAAY,OAAO,MAAM,YAAY;gCACzD,MAAM,aAAa,cAAc,mBAAmB,IAAI,YAAY;gCACpE,MAAM,WAAW,mBAAmB;gCAEpC,qBACE,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,oKAAoK,EAC9K,UAAU,+EAA+E,GAC1F,CAAC,EAAE,aAAa,4DAA4D,IAAI;;sDAGjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,uCAAuC,iCACjD;8DACC,IAAI,OAAO;;;;;;gDAEb,gCACC,6LAAC;oDACC,WAAU;oDACV,cAAc,CAAC;wDACb,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;wDAClD,oBAAoB;4DAClB,MAAM;4DACN;4DACA,UAAU;gEAAE,GAAG,KAAK,IAAI;gEAAE,GAAG,KAAK,GAAG;4DAAC;wDACxC;oDACF;oDACA,cAAc,IAAM,oBAAoB;8DAExC,cAAA,6LAAC;wDACC,OAAO,SAAS,KAAK;wDACrB,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;sDAOlB,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,KAAK,CAAC,GAAG,iBAAiB,IAAI,GAAG,GAAG,CAAC,CAAA,sBAC9C,6LAAC;wDAEC,WAAW,CAAC,qCAAqC,EAAE,kBAAkB,MAAM,IAAI,GAAG;kEAEjF,MAAM,KAAK;uDAHP,MAAM,EAAE;;;;;gDAMhB,UAAU,MAAM,GAAG,CAAC,iBAAiB,IAAI,CAAC,mBACzC,6LAAC;oDAAI,WAAU;;wDAA2C;wDACtD,UAAU,MAAM,GAAG,CAAC,iBAAiB,IAAI,CAAC;wDAAE;wDAAE,qBAAqB,IAAI;;;;;;;;;;;;;;mCA/C1E;;;;;4BAqDX;;;;;;;;;;;;;YAKH,gCACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,SAAS;oCAAC;;;;;;;4BAEjC,CAAC;gCACA,MAAM,YAAY,mBAAmB;gCACrC,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAc,OAAO,UAAU,KAAK;gDAAE,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE7D,6LAAC;4CAAG,WAAU;sDAAwD,UAAU,KAAK,CAAC,IAAI;;;;;;sDAC1F,6LAAC;4CAAE,WAAU;sDAAiD,UAAU,KAAK,CAAC,WAAW;;;;;;sDACzF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA6C,qBAAqB,GAAG;;;;;;sEACpF,6LAAC;4DAAI,WAAU;;gEAAoC,UAAU,GAAG;gEAAC;gEAAE,qBAAqB,IAAI;;;;;;;;;;;;;8DAE9F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA6C,qBAAqB,YAAY;;;;;;sEAC7F,6LAAC;4DAAI,WAAU;;gEAAoC,UAAU,YAAY;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAKpF,CAAC;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAC9B,qBAAqB,QAAQ;oCAAC;oCAAY,qBAAqB,MAAM;;;;;;;4BAEvE,CAAC;gCACA,MAAM,YAAY,mBAAmB;gCACrC,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAA6C,qBAAqB,IAAI;oEAAC;oEAAE,qBAAqB,QAAQ;;;;;;;0EACrH,6LAAC;gEAAI,WAAU;0EACZ,UAAU,YAAY,CAAC,kBAAkB,CAAC,SAAS;oEAClD,SAAS;oEACT,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP;;;;;;;;;;;;;;;;;;;;;;;sDAKR,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAA6C,qBAAqB,IAAI;oEAAC;oEAAE,qBAAqB,OAAO;;;;;;;0EACpH,6LAAC;gEAAI,WAAU;0EACZ,UAAU,WAAW,CAAC,kBAAkB,CAAC,SAAS;oEACjD,SAAS;oEACT,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOd,CAAC;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAC9B,qBAAqB,MAAM;oCAAC;oCAAK,qBAAqB,UAAU;;;;;;;0CAEnE,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAc,OAAO;gDAAO,MAAM;;;;;;0DACnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqD,MAAM,IAAI;;;;;;kEAC9E,6LAAC;wDAAI,WAAU;;4DAA4C,MAAM,YAAY;4DAAC;4DAAG,qBAAqB,YAAY;;;;;;;;;;;;;;uCAJ5G;;;;;;;;;;;;;;;;;;;;;;YAcnB,kCACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,iBAAiB,QAAQ,CAAC,CAAC;oBACjC,KAAK,iBAAiB,QAAQ,CAAC,CAAC,GAAG;oBACnC,WAAW;gBACb;0BAEA,cAAA,6LAAC;oBAAiB,UAAU,iBAAiB,QAAQ;;;;;;;;;;;0BAKzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAC9B,qBAAqB,QAAQ;4BAAC;4BAAM,qBAAqB,MAAM;;;;;;;kCAGlE,6LAAC;wBAAI,WAAU;kCACZ,OACE,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,IAAI,KAAK,OACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,sBACH,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAI,WAAW,CAAC,eAAe,EAAE,kBAAkB,MAAM,IAAI,GAAG;kDAC/D,cAAA,6LAAC,6MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6C,MAAM,KAAK;;;;;;0DACtE,6LAAC;gDAAE,WAAU;0DAAiD,MAAM,WAAW;;;;;;0DAE/E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;oEAAG;oEAAK,MAAM,IAAI;;;;;;;;;;;;;oDAGjE,MAAM,QAAQ,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,MAAM,QAAQ;;;;;;;;;;;;oDAIxB,MAAM,SAAS,kBACd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,MAAM,SAAS,CAAC,MAAM;oEAAC;oEAAE,qBAAqB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;+BAzB9D,MAAM,EAAE;;;;;;;;;;;;;;;;YAoCzB,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;gCAChC,qBAAqB,MAAM;;;;;;;sCAG7C,6LAAC;4BAAK,UAAU;4BAAgB,WAAU;;8CACxC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,KAAK;gDAAC;gDAAK,qBAAqB,MAAM;;;;;;;sDAE9D,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDACd,qBAAqB,WAAW;;;;;;sDAEnC,6LAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,qBAAqB,IAAI;;;;;;8DAE5B,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,qBAAqB,IAAI;;;;;;8DAE5B,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,IAAI;gDAAC;gDAAK,qBAAqB,MAAM;;;;;;;sDAE7D,6LAAC;4CACC,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAkB;4CAClF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;8DACvD,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;8DACvD,6LAAC;oDAAO,OAAM;8DAAW,qBAAqB,OAAO;;;;;;8DACrD,6LAAC;oDAAO,OAAM;8DAAW,qBAAqB,OAAO;;;;;;8DACrD,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;8CAI3D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,QAAQ;gDAAC;;;;;;;sDAEjC,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,qBAAqB,MAAM;;;;;;sDAE9B,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAET,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GA9iBwB;MAAA", "debugId": null}}, {"offset": {"line": 7690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/History.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Search, Download, Clock, User, Package, DollarSign,\n  Eye, Trash2, Archive, RefreshCw,\n  TrendingUp, BarChart3, FileText, Settings, CheckSquare,\n  Square, X, ChevronDown, ChevronRight, AlertCircle,\n  Activity, Zap, Shield, Database, Bell, Star\n} from 'lucide-react'\nimport { useState, useMemo } from 'react'\n\ninterface HistoryItem {\n  id: string\n  type: 'product' | 'debt' | 'payment' | 'login' | 'system' | 'security' | 'backup' | 'notification'\n  action: string\n  description: string\n  user: string\n  timestamp: string\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  status: 'success' | 'warning' | 'error' | 'info'\n  category: string\n  ipAddress?: string\n  userAgent?: string\n  details?: Record<string, unknown>\n  tags?: string[]\n}\n\ninterface FilterOptions {\n  types: string[]\n  priorities: string[]\n  statuses: string[]\n  dateRange: string\n  users: string[]\n  categories: string[]\n}\n\nexport default function History() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterType, setFilterType] = useState('all')\n  const [dateRange, setDateRange] = useState('7days')\n  const [selectedItems, setSelectedItems] = useState<string[]>([])\n  const [showFilters, setShowFilters] = useState(false)\n  const [selectedActivity, setSelectedActivity] = useState<HistoryItem | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [viewMode, setViewMode] = useState<'list' | 'timeline' | 'grid'>('list')\n  const [sortBy, setSortBy] = useState<'timestamp' | 'type' | 'priority'>('timestamp')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n\n  const [filters, setFilters] = useState<FilterOptions>({\n    types: [],\n    priorities: [],\n    statuses: [],\n    dateRange: '7days',\n    users: [],\n    categories: []\n  })\n\n  // Enhanced history data with more comprehensive information\n  const historyData: HistoryItem[] = useMemo(() => [\n    {\n      id: '1',\n      type: 'product',\n      action: 'Product Added',\n      description: 'Added \"Lucky Me Pancit Canton\" to product list',\n      user: 'Admin',\n      timestamp: '2024-01-20T10:30:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'Inventory Management',\n      tags: ['product', 'inventory', 'add'],\n      details: {\n        productName: 'Lucky Me Pancit Canton',\n        price: 15.00,\n        sku: 'LMC001',\n        quantity: 50,\n        supplier: 'Lucky Me Foods'\n      }\n    },\n    {\n      id: '2',\n      type: 'debt',\n      action: 'Debt Recorded',\n      description: 'New debt record for Juan Dela Cruz',\n      user: 'Admin',\n      timestamp: '2024-01-20T09:15:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Customer Management',\n      tags: ['debt', 'customer', 'record'],\n      details: {\n        customer: 'Juan Dela Cruz',\n        amount: 45.00,\n        dueDate: '2024-02-20',\n        contactNumber: '09123456789',\n        address: 'Barangay San Jose, Cebu City'\n      }\n    },\n    {\n      id: '3',\n      type: 'payment',\n      action: 'Payment Received',\n      description: 'Payment received from Maria Santos',\n      user: 'Admin',\n      timestamp: '2024-01-19T16:45:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'Financial Transaction',\n      tags: ['payment', 'customer', 'income'],\n      details: {\n        customer: 'Maria Santos',\n        amount: 120.00,\n        paymentMethod: 'Cash',\n        previousBalance: 200.00,\n        newBalance: 80.00\n      }\n    },\n    {\n      id: '4',\n      type: 'product',\n      action: 'Stock Updated',\n      description: 'Updated stock quantity for Coca-Cola',\n      user: 'Admin',\n      timestamp: '2024-01-19T14:20:00Z',\n      priority: 'low',\n      status: 'success',\n      category: 'Inventory Management',\n      tags: ['product', 'stock', 'update'],\n      details: {\n        productName: 'Coca-Cola',\n        oldStock: 25,\n        newStock: 50,\n        reason: 'New delivery received',\n        supplier: 'Coca-Cola Bottlers Philippines'\n      }\n    },\n    {\n      id: '5',\n      type: 'login',\n      action: 'User Login',\n      description: 'Admin user logged into the system',\n      user: 'Admin',\n      timestamp: '2024-01-19T08:00:00Z',\n      priority: 'low',\n      status: 'info',\n      category: 'Security',\n      tags: ['login', 'security', 'access'],\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      details: {\n        ipAddress: '*************',\n        location: 'Cebu City, Philippines',\n        device: 'Desktop - Windows 10',\n        sessionDuration: '4 hours 30 minutes'\n      }\n    },\n    {\n      id: '6',\n      type: 'system',\n      action: 'Backup Created',\n      description: 'Automatic database backup completed',\n      user: 'System',\n      timestamp: '2024-01-19T02:00:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'System Maintenance',\n      tags: ['backup', 'database', 'maintenance'],\n      details: {\n        backupSize: '2.5MB',\n        backupType: 'Full Backup',\n        location: 'Cloud Storage',\n        duration: '45 seconds',\n        tablesBackedUp: 8\n      }\n    },\n    {\n      id: '7',\n      type: 'debt',\n      action: 'Debt Updated',\n      description: 'Updated debt record for Ana Reyes',\n      user: 'Admin',\n      timestamp: '2024-01-18T15:30:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Customer Management',\n      tags: ['debt', 'customer', 'update'],\n      details: {\n        customer: 'Ana Reyes',\n        oldAmount: 75.00,\n        newAmount: 100.00,\n        reason: 'Additional purchase',\n        dueDate: '2024-02-18',\n        contactNumber: '09987654321'\n      }\n    },\n    {\n      id: '8',\n      type: 'product',\n      action: 'Product Deleted',\n      description: 'Removed \"Expired Milk\" from product list',\n      user: 'Admin',\n      timestamp: '2024-01-18T11:10:00Z',\n      priority: 'high',\n      status: 'error',\n      category: 'Inventory Management',\n      tags: ['product', 'delete', 'expired'],\n      details: {\n        productName: 'Expired Milk',\n        reason: 'Product expired',\n        expiryDate: '2024-01-15',\n        quantityRemoved: 12,\n        loss: 180.00\n      }\n    },\n    {\n      id: '9',\n      type: 'security',\n      action: 'Failed Login Attempt',\n      description: 'Multiple failed login attempts detected',\n      user: 'Unknown',\n      timestamp: '2024-01-18T03:45:00Z',\n      priority: 'critical',\n      status: 'error',\n      category: 'Security Alert',\n      tags: ['security', 'login', 'failed'],\n      ipAddress: '*************',\n      details: {\n        attempts: 5,\n        ipAddress: '*************',\n        location: 'Unknown',\n        blocked: true,\n        duration: '24 hours'\n      }\n    },\n    {\n      id: '10',\n      type: 'notification',\n      action: 'Low Stock Alert',\n      description: 'Stock level below minimum threshold for multiple products',\n      user: 'System',\n      timestamp: '2024-01-17T18:00:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Inventory Alert',\n      tags: ['stock', 'alert', 'inventory'],\n      details: {\n        affectedProducts: ['Rice 25kg', 'Cooking Oil 1L', 'Sugar 1kg'],\n        minimumThreshold: 10,\n        currentStock: [5, 3, 7],\n        recommendedOrder: [50, 20, 30]\n      }\n    },\n  ], [])\n\n  // Enhanced utility functions\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'product':\n        return <Package className=\"h-4 w-4\" />\n      case 'debt':\n        return <DollarSign className=\"h-4 w-4 text-red-500\" />\n      case 'payment':\n        return <DollarSign className=\"h-4 w-4 text-green-500\" />\n      case 'login':\n        return <User className=\"h-4 w-4\" />\n      case 'system':\n        return <Database className=\"h-4 w-4\" />\n      case 'security':\n        return <Shield className=\"h-4 w-4\" />\n      case 'backup':\n        return <Archive className=\"h-4 w-4\" />\n      case 'notification':\n        return <Bell className=\"h-4 w-4\" />\n      default:\n        return <Activity className=\"h-4 w-4\" />\n    }\n  }\n\n  const getPriorityIcon = (priority: string) => {\n    switch (priority) {\n      case 'critical':\n        return <AlertCircle className=\"h-4 w-4 text-red-600\" />\n      case 'high':\n        return <Zap className=\"h-4 w-4 text-orange-500\" />\n      case 'medium':\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      case 'low':\n        return <Star className=\"h-4 w-4 text-blue-500\" />\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'product':\n        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800'\n      case 'debt':\n        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800'\n      case 'payment':\n        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800'\n      case 'login':\n        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800'\n      case 'system':\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'\n      case 'security':\n        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800'\n      case 'backup':\n        return 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800'\n      case 'notification':\n        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800'\n      default:\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success':\n        return 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n      case 'warning':\n        return 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'\n      case 'error':\n        return 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'\n      case 'info':\n        return 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800'\n      default:\n        return 'bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800'\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical':\n        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700'\n      case 'high':\n        return 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700'\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700'\n      case 'low':\n        return 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-700'\n    }\n  }\n\n  // Advanced filtering and sorting logic\n  const filteredHistory = useMemo(() => {\n    const filtered = historyData.filter(item => {\n      // Search filter\n      const matchesSearch = searchTerm === '' ||\n        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.action.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.user.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))\n\n      // Type filter\n      const matchesType = filterType === 'all' || item.type === filterType\n\n      // Advanced filters\n      const matchesTypes = filters.types.length === 0 || filters.types.includes(item.type)\n      const matchesPriorities = filters.priorities.length === 0 || filters.priorities.includes(item.priority)\n      const matchesStatuses = filters.statuses.length === 0 || filters.statuses.includes(item.status)\n      const matchesUsers = filters.users.length === 0 || filters.users.includes(item.user)\n      const matchesCategories = filters.categories.length === 0 || filters.categories.includes(item.category)\n\n      // Date range filter\n      const itemDate = new Date(item.timestamp)\n      const now = new Date()\n      let matchesDateRange = true\n\n      switch (dateRange) {\n        case '24hours':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 24 * 60 * 60 * 1000\n          break\n        case '7days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 7 * 24 * 60 * 60 * 1000\n          break\n        case '30days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 30 * 24 * 60 * 60 * 1000\n          break\n        case '90days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 90 * 24 * 60 * 60 * 1000\n          break\n        case 'all':\n          matchesDateRange = true\n          break\n      }\n\n      return matchesSearch && matchesType && matchesTypes && matchesPriorities &&\n             matchesStatuses && matchesUsers && matchesCategories && matchesDateRange\n    })\n\n    // Sorting\n    filtered.sort((a, b) => {\n      let comparison = 0\n\n      switch (sortBy) {\n        case 'timestamp':\n          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n          break\n        case 'type':\n          comparison = a.type.localeCompare(b.type)\n          break\n        case 'priority':\n          const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }\n          comparison = (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) -\n                      (priorityOrder[b.priority as keyof typeof priorityOrder] || 0)\n          break\n      }\n\n      return sortOrder === 'desc' ? -comparison : comparison\n    })\n\n    return filtered\n  }, [historyData, searchTerm, filterType, filters, dateRange, sortBy, sortOrder])\n\n  // Get unique values for filter options\n  const uniquePriorities = [...new Set(historyData.map(item => item.priority))]\n  const uniqueStatuses = [...new Set(historyData.map(item => item.status))]\n  const uniqueUsers = [...new Set(historyData.map(item => item.user))]\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    const diffInHours = Math.floor(diffInMinutes / 60)\n    const diffInDays = Math.floor(diffInHours / 24)\n\n    if (diffInMinutes < 1) {\n      return 'Just now'\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`\n    } else if (diffInHours < 24) {\n      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`\n    } else if (diffInDays < 7) {\n      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`\n    } else {\n      return date.toLocaleDateString('en-PH', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    }\n  }\n\n  // Bulk operations\n  const handleSelectAll = () => {\n    if (selectedItems.length === filteredHistory.length) {\n      setSelectedItems([])\n    } else {\n      setSelectedItems(filteredHistory.map(item => item.id))\n    }\n  }\n\n  const handleSelectItem = (id: string) => {\n    setSelectedItems(prev =>\n      prev.includes(id)\n        ? prev.filter(item => item !== id)\n        : [...prev, id]\n    )\n  }\n\n  const handleBulkDelete = () => {\n    if (selectedItems.length > 0) {\n      // In a real app, this would make an API call\n      // For now, just clear the selection\n      setSelectedItems([])\n    }\n  }\n\n  const handleBulkExport = () => {\n    const selectedData = filteredHistory.filter(item => selectedItems.includes(item.id))\n    exportHistory(selectedData)\n  }\n\n  const exportHistory = (data = filteredHistory) => {\n    const csvContent = [\n      ['Timestamp', 'Type', 'Action', 'Description', 'User', 'Priority', 'Status', 'Category'],\n      ...data.map(item => [\n        item.timestamp,\n        item.type,\n        item.action,\n        item.description,\n        item.user,\n        item.priority,\n        item.status,\n        item.category\n      ])\n    ].map(row => row.join(',')).join('\\n')\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `revantad-store-history-${new Date().toISOString().split('T')[0]}.csv`\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  // Activity statistics\n  const activityStats = useMemo(() => {\n    const stats = {\n      total: filteredHistory.length,\n      byType: {} as Record<string, number>,\n      byPriority: {} as Record<string, number>,\n      byStatus: {} as Record<string, number>,\n      byUser: {} as Record<string, number>,\n      todayCount: 0,\n      weekCount: 0\n    }\n\n    const now = new Date()\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)\n\n    filteredHistory.forEach(item => {\n      // Count by type\n      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1\n\n      // Count by priority\n      stats.byPriority[item.priority] = (stats.byPriority[item.priority] || 0) + 1\n\n      // Count by status\n      stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1\n\n      // Count by user\n      stats.byUser[item.user] = (stats.byUser[item.user] || 0) + 1\n\n      // Count today and this week\n      const itemDate = new Date(item.timestamp)\n      if (itemDate >= today) {\n        stats.todayCount++\n      }\n      if (itemDate >= weekAgo) {\n        stats.weekCount++\n      }\n    })\n\n    return stats\n  }, [filteredHistory])\n\n  return (\n    <div className=\"space-y-6 animate-fade-in\">\n      {/* Enhanced Header with Stats */}\n      <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center gap-3 mb-2\">\n            <div className=\"p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg\">\n              <Activity className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Activity History\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                Comprehensive tracking ng lahat ng activities sa inyong store\n              </p>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4\">\n            <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-blue-600 dark:text-blue-400\">Total Activities</p>\n                  <p className=\"text-lg font-bold text-blue-900 dark:text-blue-300\">{activityStats.total}</p>\n                </div>\n                <BarChart3 className=\"h-5 w-5 text-blue-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-green-600 dark:text-green-400\">Today</p>\n                  <p className=\"text-lg font-bold text-green-900 dark:text-green-300\">{activityStats.todayCount}</p>\n                </div>\n                <TrendingUp className=\"h-5 w-5 text-green-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-yellow-600 dark:text-yellow-400\">This Week</p>\n                  <p className=\"text-lg font-bold text-yellow-900 dark:text-yellow-300\">{activityStats.weekCount}</p>\n                </div>\n                <Clock className=\"h-5 w-5 text-yellow-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-purple-600 dark:text-purple-400\">Selected</p>\n                  <p className=\"text-lg font-bold text-purple-900 dark:text-purple-300\">{selectedItems.length}</p>\n                </div>\n                <CheckSquare className=\"h-5 w-5 text-purple-500\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-2\">\n          <button\n            onClick={() => setIsLoading(!isLoading)}\n            className=\"btn-outline flex items-center gap-2 text-sm\"\n            disabled={isLoading}\n          >\n            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n            Refresh\n          </button>\n\n          {selectedItems.length > 0 && (\n            <>\n              <button\n                onClick={handleBulkExport}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <Download className=\"h-4 w-4\" />\n                Export Selected\n              </button>\n\n              <button\n                onClick={handleBulkDelete}\n                className=\"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n                Delete Selected\n              </button>\n            </>\n          )}\n\n          <button\n            onClick={() => exportHistory()}\n            className=\"btn-primary flex items-center gap-2 text-sm\"\n          >\n            <Download className=\"h-4 w-4\" />\n            Export All\n          </button>\n        </div>\n      </div>\n\n      {/* Enhanced Filters */}\n      <div className=\"card p-6 border-l-4 border-l-green-500\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n            <Search className=\"h-5 w-5 text-green-500\" />\n            Search & Filters\n          </h3>\n          <div className=\"flex items-center gap-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors\"\n            >\n              {showFilters ? <ChevronDown className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              Advanced Filters\n            </button>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n              {(['list', 'timeline', 'grid'] as const).map((mode) => (\n                <button\n                  key={mode}\n                  onClick={() => setViewMode(mode)}\n                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${\n                    viewMode === mode\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  }`}\n                >\n                  {mode.charAt(0).toUpperCase() + mode.slice(1)}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Basic Filters */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search activities, users, descriptions...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            />\n          </div>\n\n          {/* Type Filter */}\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"product\">Product Activities</option>\n            <option value=\"debt\">Debt Activities</option>\n            <option value=\"payment\">Payment Activities</option>\n            <option value=\"login\">Login Activities</option>\n            <option value=\"system\">System Activities</option>\n            <option value=\"security\">Security Activities</option>\n            <option value=\"notification\">Notifications</option>\n          </select>\n\n          {/* Date Range */}\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n          >\n            <option value=\"24hours\">Last 24 hours</option>\n            <option value=\"7days\">Last 7 days</option>\n            <option value=\"30days\">Last 30 days</option>\n            <option value=\"90days\">Last 90 days</option>\n            <option value=\"all\">All time</option>\n          </select>\n\n          {/* Sort Options */}\n          <div className=\"flex gap-2\">\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'type' | 'priority')}\n              className=\"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            >\n              <option value=\"timestamp\">Sort by Time</option>\n              <option value=\"type\">Sort by Type</option>\n              <option value=\"priority\">Sort by Priority</option>\n            </select>\n            <button\n              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n              className=\"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}\n            >\n              {sortOrder === 'asc' ? '↑' : '↓'}\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Filters (Collapsible) */}\n        {showFilters && (\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Priority Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Priority Levels\n                </label>\n                <div className=\"space-y-2\">\n                  {uniquePriorities.map(priority => (\n                    <label key={priority} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.priorities.includes(priority)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              priorities: [...prev.priorities, priority]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              priorities: prev.priorities.filter(p => p !== priority)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1\">\n                        {getPriorityIcon(priority)}\n                        {priority}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Status Types\n                </label>\n                <div className=\"space-y-2\">\n                  {uniqueStatuses.map(status => (\n                    <label key={status} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.statuses.includes(status)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              statuses: [...prev.statuses, status]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              statuses: prev.statuses.filter(s => s !== status)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>\n                        {status}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* User Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Users\n                </label>\n                <div className=\"space-y-2\">\n                  {uniqueUsers.map(user => (\n                    <label key={user} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.users.includes(user)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              users: [...prev.users, user]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              users: prev.users.filter(u => u !== user)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1\">\n                        <User className=\"h-3 w-3\" />\n                        {user}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Clear Filters */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                onClick={() => {\n                  setFilters({\n                    types: [],\n                    priorities: [],\n                    statuses: [],\n                    dateRange: '7days',\n                    users: [],\n                    categories: []\n                  })\n                  setSearchTerm('')\n                  setFilterType('all')\n                  setDateRange('7days')\n                }}\n                className=\"text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1 transition-colors\"\n              >\n                <X className=\"h-4 w-4\" />\n                Clear All Filters\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Activity Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n        {Object.entries(activityStats.byType).map(([type, count]) => (\n          <div key={type} className=\"card p-4 text-center hover:shadow-lg transition-all duration-200 hover:-translate-y-1 cursor-pointer group\">\n            <div className={`inline-flex p-3 rounded-xl mb-3 transition-all group-hover:scale-110 ${getTypeColor(type)}`}>\n              {getTypeIcon(type)}\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">{count}</p>\n            <p className=\"text-xs text-gray-600 dark:text-gray-400 capitalize font-medium\">\n              {type} Activities\n            </p>\n          </div>\n        ))}\n      </div>\n\n      {/* Enhanced Activity List */}\n      <div className=\"card overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n                <FileText className=\"h-5 w-5 text-green-500\" />\n                Activity Timeline ({filteredHistory.length})\n              </h3>\n\n              {/* Bulk Selection */}\n              {filteredHistory.length > 0 && (\n                <div className=\"flex items-center gap-2\">\n                  <button\n                    onClick={handleSelectAll}\n                    className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                  >\n                    {selectedItems.length === filteredHistory.length ? (\n                      <CheckSquare className=\"h-4 w-4 text-green-500\" />\n                    ) : (\n                      <Square className=\"h-4 w-4\" />\n                    )}\n                    Select All\n                  </button>\n\n                  {selectedItems.length > 0 && (\n                    <span className=\"text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded-full\">\n                      {selectedItems.length} selected\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Loading Indicator */}\n            {isLoading && (\n              <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\">\n                <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                Loading...\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto main-content-scroll\">\n          {filteredHistory.map((item, index) => (\n            <div\n              key={item.id}\n              className={`p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group ${\n                selectedItems.includes(item.id) ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' : ''\n              }`}\n              style={{ animationDelay: `${index * 50}ms` }}\n            >\n              <div className=\"flex items-start space-x-4\">\n                {/* Selection Checkbox */}\n                <button\n                  onClick={() => handleSelectItem(item.id)}\n                  className=\"mt-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  {selectedItems.includes(item.id) ? (\n                    <CheckSquare className=\"h-4 w-4 text-green-500\" />\n                  ) : (\n                    <Square className=\"h-4 w-4 text-gray-400 hover:text-gray-600\" />\n                  )}\n                </button>\n\n                {/* Activity Icon */}\n                <div className={`p-3 rounded-xl shadow-sm ${getTypeColor(item.type)} transition-all group-hover:scale-105`}>\n                  {getTypeIcon(item.type)}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  {/* Header */}\n                  <div className=\"flex items-start justify-between gap-4 mb-2\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white\">\n                          {item.action}\n                        </h4>\n\n                        {/* Priority Badge */}\n                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>\n                          {getPriorityIcon(item.priority)}\n                          {item.priority}\n                        </span>\n\n                        {/* Status Badge */}\n                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>\n                          {item.status}\n                        </span>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 leading-relaxed\">\n                        {item.description}\n                      </p>\n\n                      {/* Tags */}\n                      {item.tags && item.tags.length > 0 && (\n                        <div className=\"flex flex-wrap gap-1 mt-2\">\n                          {item.tags.map(tag => (\n                            <span\n                              key={tag}\n                              className=\"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md\"\n                            >\n                              #{tag}\n                            </span>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"text-right\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 font-medium\">\n                        {formatTimestamp(item.timestamp)}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Footer */}\n                  <div className=\"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700\">\n                    <div className=\"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n                      <span className=\"flex items-center gap-1\">\n                        <User className=\"h-3 w-3\" />\n                        {item.user}\n                      </span>\n\n                      <span className=\"flex items-center gap-1\">\n                        <Settings className=\"h-3 w-3\" />\n                        {item.category}\n                      </span>\n\n                      {item.ipAddress && (\n                        <span className=\"flex items-center gap-1\">\n                          <Shield className=\"h-3 w-3\" />\n                          {item.ipAddress}\n                        </span>\n                      )}\n                    </div>\n\n                    {item.details && (\n                      <button\n                        onClick={() => setSelectedActivity(item)}\n                        className=\"text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 font-medium flex items-center gap-1 transition-colors\"\n                      >\n                        <Eye className=\"h-3 w-3\" />\n                        View Details\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Enhanced Empty State */}\n        {filteredHistory.length === 0 && (\n          <div className=\"p-16 text-center\">\n            <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\">\n              <Activity className=\"h-12 w-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3\">\n              Walang Activities na Nakita\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              Try adjusting your search terms or filter criteria to find the activities you&apos;re looking for.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n              <button\n                onClick={() => {\n                  setSearchTerm('')\n                  setFilterType('all')\n                  setFilters({\n                    types: [],\n                    priorities: [],\n                    statuses: [],\n                    dateRange: '7days',\n                    users: [],\n                    categories: []\n                  })\n                }}\n                className=\"btn-outline\"\n              >\n                Clear Filters\n              </button>\n              <button\n                onClick={() => setIsLoading(!isLoading)}\n                className=\"btn-primary\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Refresh Data\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Activity Details Modal */}\n      {selectedActivity && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div className={`p-3 rounded-xl ${getTypeColor(selectedActivity.type)}`}>\n                    {getTypeIcon(selectedActivity.type)}\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      Activity Details\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {selectedActivity.action}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setSelectedActivity(null)}\n                  className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"h-5 w-5 text-gray-500\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <div className=\"p-6 space-y-6\">\n              {/* Basic Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Activity Type\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(selectedActivity.type)}`}>\n                        {getTypeIcon(selectedActivity.type)}\n                        {selectedActivity.type}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Priority Level\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedActivity.priority)}`}>\n                        {getPriorityIcon(selectedActivity.priority)}\n                        {selectedActivity.priority}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Status\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedActivity.status)}`}>\n                        {selectedActivity.status}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      User\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <User className=\"h-4 w-4\" />\n                      {selectedActivity.user}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Category\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <Settings className=\"h-4 w-4\" />\n                      {selectedActivity.category}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Timestamp\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      {new Date(selectedActivity.timestamp).toLocaleString('en-PH')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Description */}\n              <div>\n                <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                  Description\n                </label>\n                <p className=\"text-sm text-gray-900 dark:text-white mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  {selectedActivity.description}\n                </p>\n              </div>\n\n              {/* Tags */}\n              {selectedActivity.tags && selectedActivity.tags.length > 0 && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Tags\n                  </label>\n                  <div className=\"flex flex-wrap gap-2 mt-2\">\n                    {selectedActivity.tags.map(tag => (\n                      <span\n                        key={tag}\n                        className=\"inline-flex px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-sm rounded-full\"\n                      >\n                        #{tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Additional Details */}\n              {selectedActivity.details && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Additional Details\n                  </label>\n                  <div className=\"mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <pre className=\"text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono\">\n                      {JSON.stringify(selectedActivity.details, null, 2)}\n                    </pre>\n                  </div>\n                </div>\n              )}\n\n              {/* Security Information */}\n              {(selectedActivity.ipAddress || selectedActivity.userAgent) && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Security Information\n                  </label>\n                  <div className=\"mt-2 space-y-2\">\n                    {selectedActivity.ipAddress && (\n                      <p className=\"text-sm text-gray-900 dark:text-white flex items-center gap-2\">\n                        <Shield className=\"h-4 w-4 text-orange-500\" />\n                        <span className=\"font-medium\">IP Address:</span>\n                        {selectedActivity.ipAddress}\n                      </p>\n                    )}\n                    {selectedActivity.userAgent && (\n                      <p className=\"text-sm text-gray-900 dark:text-white flex items-center gap-2\">\n                        <Settings className=\"h-4 w-4 text-blue-500\" />\n                        <span className=\"font-medium\">User Agent:</span>\n                        <span className=\"truncate\">{selectedActivity.userAgent}</span>\n                      </p>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50\">\n              <div className=\"flex justify-end gap-3\">\n                <button\n                  onClick={() => setSelectedActivity(null)}\n                  className=\"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  Close\n                </button>\n                <button\n                  onClick={() => {\n                    exportHistory([selectedActivity])\n                    setSelectedActivity(null)\n                  }}\n                  className=\"btn-primary flex items-center gap-2\"\n                >\n                  <Download className=\"h-4 w-4\" />\n                  Export This Activity\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AATA;;;AAoCe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO,EAAE;QACT,YAAY,EAAE;QACd,UAAU,EAAE;QACZ,WAAW;QACX,OAAO,EAAE;QACT,YAAY,EAAE;IAChB;IAEA,4DAA4D;IAC5D,MAAM,cAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE,IAAM;gBAC/C;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAa;qBAAM;oBACrC,SAAS;wBACP,aAAa;wBACb,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAQ;wBAAY;qBAAS;oBACpC,SAAS;wBACP,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAY;qBAAS;oBACvC,SAAS;wBACP,UAAU;wBACV,QAAQ;wBACR,eAAe;wBACf,iBAAiB;wBACjB,YAAY;oBACd;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAS;qBAAS;oBACpC,SAAS;wBACP,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAS;wBAAY;qBAAS;oBACrC,WAAW;oBACX,WAAW;oBACX,SAAS;wBACP,WAAW;wBACX,UAAU;wBACV,QAAQ;wBACR,iBAAiB;oBACnB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAU;wBAAY;qBAAc;oBAC3C,SAAS;wBACP,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,gBAAgB;oBAClB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAQ;wBAAY;qBAAS;oBACpC,SAAS;wBACP,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,eAAe;oBACjB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAU;qBAAU;oBACtC,SAAS;wBACP,aAAa;wBACb,QAAQ;wBACR,YAAY;wBACZ,iBAAiB;wBACjB,MAAM;oBACR;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAY;wBAAS;qBAAS;oBACrC,WAAW;oBACX,SAAS;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,SAAS;wBACT,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAS;wBAAS;qBAAY;oBACrC,SAAS;wBACP,kBAAkB;4BAAC;4BAAa;4BAAkB;yBAAY;wBAC9D,kBAAkB;wBAClB,cAAc;4BAAC;4BAAG;4BAAG;yBAAE;wBACvB,kBAAkB;4BAAC;4BAAI;4BAAI;yBAAG;oBAChC;gBACF;aACD;uCAAE,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC9B,MAAM,WAAW,YAAY,MAAM;6DAAC,CAAA;oBAClC,gBAAgB;oBAChB,MAAM,gBAAgB,eAAe,MACnC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI;qEAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;oBAEvF,cAAc;oBACd,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;oBAE1D,mBAAmB;oBACnB,MAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;oBACnF,MAAM,oBAAoB,QAAQ,UAAU,CAAC,MAAM,KAAK,KAAK,QAAQ,UAAU,CAAC,QAAQ,CAAC,KAAK,QAAQ;oBACtG,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,MAAM,KAAK,KAAK,QAAQ,QAAQ,CAAC,QAAQ,CAAC,KAAK,MAAM;oBAC9F,MAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;oBACnF,MAAM,oBAAoB,QAAQ,UAAU,CAAC,MAAM,KAAK,KAAK,QAAQ,UAAU,CAAC,QAAQ,CAAC,KAAK,QAAQ;oBAEtG,oBAAoB;oBACpB,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;oBACxC,MAAM,MAAM,IAAI;oBAChB,IAAI,mBAAmB;oBAEvB,OAAQ;wBACN,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK;4BAC1E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,IAAI,KAAK,KAAK,KAAK;4BAC9E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK,KAAK;4BAC/E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK,KAAK;4BAC/E;wBACF,KAAK;4BACH,mBAAmB;4BACnB;oBACJ;oBAEA,OAAO,iBAAiB,eAAe,gBAAgB,qBAChD,mBAAmB,gBAAgB,qBAAqB;gBACjE;;YAEA,UAAU;YACV,SAAS,IAAI;oDAAC,CAAC,GAAG;oBAChB,IAAI,aAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;4BAC5E;wBACF,KAAK;4BACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACxC;wBACF,KAAK;4BACH,MAAM,gBAAgB;gCAAE,YAAY;gCAAG,QAAQ;gCAAG,UAAU;gCAAG,OAAO;4BAAE;4BACxE,aAAa,CAAC,aAAa,CAAC,EAAE,QAAQ,CAA+B,IAAI,CAAC,IAC9D,CAAC,aAAa,CAAC,EAAE,QAAQ,CAA+B,IAAI,CAAC;4BACzE;oBACJ;oBAEA,OAAO,cAAc,SAAS,CAAC,aAAa;gBAC9C;;YAEA,OAAO;QACT;2CAAG;QAAC;QAAa;QAAY;QAAY;QAAS;QAAW;QAAQ;KAAU;IAE/E,uCAAuC;IACvC,MAAM,mBAAmB;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;KAAG;IAC7E,MAAM,iBAAiB;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;KAAG;IACzE,MAAM,cAAc;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;KAAG;IAEpE,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAC9E,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAE5C,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;QACrE,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;QAC/D,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;QAC5D,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB;QACtB,IAAI,cAAc,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACnD,iBAAiB,EAAE;QACrB,OAAO;YACL,iBAAiB,gBAAgB,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;QACtD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,MAC7B;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,6CAA6C;YAC7C,oCAAoC;YACpC,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;QAClF,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC,OAAO,eAAe;QAC3C,MAAM,aAAa;YACjB;gBAAC;gBAAa;gBAAQ;gBAAU;gBAAe;gBAAQ;gBAAY;gBAAU;aAAW;eACrF,KAAK,GAAG,CAAC,CAAA,OAAQ;oBAClB,KAAK,SAAS;oBACd,KAAK,IAAI;oBACT,KAAK,MAAM;oBACX,KAAK,WAAW;oBAChB,KAAK,IAAI;oBACT,KAAK,QAAQ;oBACb,KAAK,MAAM;oBACX,KAAK,QAAQ;iBACd;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;QAEjC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnF,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YAC5B,MAAM,QAAQ;gBACZ,OAAO,gBAAgB,MAAM;gBAC7B,QAAQ,CAAC;gBACT,YAAY,CAAC;gBACb,UAAU,CAAC;gBACX,QAAQ,CAAC;gBACT,YAAY;gBACZ,WAAW;YACb;YAEA,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;YACrE,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YAE5D,gBAAgB,OAAO;kDAAC,CAAA;oBACtB,gBAAgB;oBAChB,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;oBAE3D,oBAAoB;oBACpB,MAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;oBAE3E,kBAAkB;oBAClB,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;oBAEnE,gBAAgB;oBAChB,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;oBAE3D,4BAA4B;oBAC5B,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;oBACxC,IAAI,YAAY,OAAO;wBACrB,MAAM,UAAU;oBAClB;oBACA,IAAI,YAAY,SAAS;wBACvB,MAAM,SAAS;oBACjB;gBACF;;YAEA,OAAO;QACT;yCAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAOpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAAsD,cAAc,KAAK;;;;;;;;;;;;8DAExF,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAyD;;;;;;sEACtE,6LAAC;4DAAE,WAAU;sEAAwD,cAAc,UAAU;;;;;;;;;;;;8DAE/F,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA2D;;;;;;sEACxE,6LAAC;4DAAE,WAAU;sEAA0D,cAAc,SAAS;;;;;;;;;;;;8DAEhG,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA2D;;;;;;sEACxE,6LAAC;4DAAE,WAAU;sEAA0D,cAAc,MAAM;;;;;;;;;;;;8DAE7F,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa,CAAC;gCAC7B,WAAU;gCACV,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;4BAIvE,cAAc,MAAM,GAAG,mBACtB;;kDACE,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIlC,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;0CAMpC,6LAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;4CAET,4BAAc,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAAe,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAK5F,6LAAC;wCAAI,WAAU;kDACZ,AAAC;4CAAC;4CAAQ;4CAAY;yCAAO,CAAW,GAAG,CAAC,CAAC,qBAC5C,6LAAC;gDAEC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,wDAAwD,EAClE,aAAa,OACT,sEACA,8EACJ;0DAED,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;+CARtC;;;;;;;;;;;;;;;;;;;;;;kCAgBf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAe;;;;;;;;;;;;0CAI/B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;kDAE3B,6LAAC;wCACC,SAAS,IAAM,aAAa,cAAc,QAAQ,SAAS;wCAC3D,WAAU;wCACV,OAAO,CAAC,KAAK,EAAE,cAAc,QAAQ,eAAe,aAAa;kDAEhE,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;oBAMlC,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;wDAAqB,WAAU;;0EAC9B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,UAAU,CAAC,QAAQ,CAAC;gEACrC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,YAAY;uFAAI,KAAK,UAAU;oFAAE;iFAAS;4EAC5C,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EAChD,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;oEACb,gBAAgB;oEAChB;;;;;;;;uDArBO;;;;;;;;;;;;;;;;kDA6BlB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;wDAAmB,WAAU;;0EAC5B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC;gEACnC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,UAAU;uFAAI,KAAK,QAAQ;oFAAE;iFAAO;4EACtC,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EAC5C,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAW,CAAC,gDAAgD,EAAE,eAAe,SAAS;0EACzF;;;;;;;uDApBO;;;;;;;;;;;;;;;;kDA4BlB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,KAAK,CAAC,QAAQ,CAAC;gEAChC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,OAAO;uFAAI,KAAK,KAAK;oFAAE;iFAAK;4EAC9B,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EACtC,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf;;;;;;;;uDArBO;;;;;;;;;;;;;;;;;;;;;;0CA8BpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,WAAW;4CACT,OAAO,EAAE;4CACT,YAAY,EAAE;4CACd,UAAU,EAAE;4CACZ,WAAW;4CACX,OAAO,EAAE;4CACT,YAAY,EAAE;wCAChB;wCACA,cAAc;wCACd,cAAc;wCACd,aAAa;oCACf;oCACA,WAAU;;sDAEV,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,cAAc,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBACtD,6LAAC;wBAAe,WAAU;;0CACxB,6LAAC;gCAAI,WAAW,CAAC,qEAAqE,EAAE,aAAa,OAAO;0CACzG,YAAY;;;;;;0CAEf,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CACtE,6LAAC;gCAAE,WAAU;;oCACV;oCAAK;;;;;;;;uBANA;;;;;;;;;;0BAad,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2B;gDAC3B,gBAAgB,MAAM;gDAAC;;;;;;;wCAI5C,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;;wDAET,cAAc,MAAM,KAAK,gBAAgB,MAAM,iBAC9C,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAClB;;;;;;;gDAIH,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAK,WAAU;;wDACb,cAAc,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;gCAQ/B,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAyB;;;;;;;;;;;;;;;;;;kCAOtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;gCAEC,WAAW,CAAC,kFAAkF,EAC5F,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,mEAAmE,IACrG;gCACF,OAAO;oCAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gCAAC;0CAE3C,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4CACvC,WAAU;sDAET,cAAc,QAAQ,CAAC,KAAK,EAAE,kBAC7B,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAKtB,6LAAC;4CAAI,WAAW,CAAC,yBAAyB,EAAE,aAAa,KAAK,IAAI,EAAE,qCAAqC,CAAC;sDACvG,YAAY,KAAK,IAAI;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,KAAK,MAAM;;;;;;sFAId,6LAAC;4EAAK,WAAW,CAAC,0EAA0E,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;gFAC5H,gBAAgB,KAAK,QAAQ;gFAC7B,KAAK,QAAQ;;;;;;;sFAIhB,6LAAC;4EAAK,WAAW,CAAC,uDAAuD,EAAE,eAAe,KAAK,MAAM,GAAG;sFACrG,KAAK,MAAM;;;;;;;;;;;;8EAIhB,6LAAC;oEAAE,WAAU;8EACV,KAAK,WAAW;;;;;;gEAIlB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oEAAI,WAAU;8EACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,oBACb,6LAAC;4EAEC,WAAU;;gFACX;gFACG;;2EAHG;;;;;;;;;;;;;;;;sEAUf,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,gBAAgB,KAAK,SAAS;;;;;;;;;;;;;;;;;8DAMrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf,KAAK,IAAI;;;;;;;8EAGZ,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,KAAK,QAAQ;;;;;;;gEAGf,KAAK,SAAS,kBACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,KAAK,SAAS;;;;;;;;;;;;;wDAKpB,KAAK,OAAO,kBACX,6LAAC;4DACC,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;8EAEV,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;+BAjGhC,KAAK,EAAE;;;;;;;;;;oBA6GjB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,cAAc;4CACd,cAAc;4CACd,WAAW;gDACT,OAAO,EAAE;gDACT,YAAY,EAAE;gDACd,UAAU,EAAE;gDACZ,WAAW;gDACX,OAAO,EAAE;gDACT,YAAY,EAAE;4CAChB;wCACF;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa,CAAC;wCAC7B,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAS/C,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,eAAe,EAAE,aAAa,iBAAiB,IAAI,GAAG;0DACpE,YAAY,iBAAiB,IAAI;;;;;;0DAEpC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,6LAAC;wDAAE,WAAU;kEACV,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;kDAI9B,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,0EAA0E,EAAE,aAAa,iBAAiB,IAAI,GAAG;;oEAChI,YAAY,iBAAiB,IAAI;oEACjC,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;8DAK5B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,0EAA0E,EAAE,iBAAiB,iBAAiB,QAAQ,GAAG;;oEACxI,gBAAgB,iBAAiB,QAAQ;oEACzC,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;8DAKhC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,uDAAuD,EAAE,eAAe,iBAAiB,MAAM,GAAG;0EACjH,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sDAMhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,iBAAiB,IAAI;;;;;;;;;;;;;8DAI1B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,iBAAiB,QAAQ;;;;;;;;;;;;;8DAI9B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,IAAI,KAAK,iBAAiB,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAE,WAAU;sDACV,iBAAiB,WAAW;;;;;;;;;;;;gCAKhC,iBAAiB,IAAI,IAAI,iBAAiB,IAAI,CAAC,MAAM,GAAG,mBACvD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;sDACZ,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAA,oBACzB,6LAAC;oDAEC,WAAU;;wDACX;wDACG;;mDAHG;;;;;;;;;;;;;;;;gCAWd,iBAAiB,OAAO,kBACvB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,iBAAiB,OAAO,EAAE,MAAM;;;;;;;;;;;;;;;;;gCAOvD,CAAC,iBAAiB,SAAS,IAAI,iBAAiB,SAAS,mBACxD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;;gDACZ,iBAAiB,SAAS,kBACzB,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAC7B,iBAAiB,SAAS;;;;;;;gDAG9B,iBAAiB,SAAS,kBACzB,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,6LAAC;4DAAK,WAAU;sEAAY,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;4CACP,cAAc;gDAAC;6CAAiB;4CAChC,oBAAoB;wCACtB;wCACA,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD;GAxuCwB;KAAA", "debugId": null}}, {"offset": {"line": 10343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { \n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Calendar, Globe, Camera, Eye, EyeOff, Key, Smartphone,\n  Mail, Phone, Building, CreditCard, FileText, Settings as SettingsIcon,\n  Trash2, Plus, Edit3, Check, X, AlertTriangle, Info, Star, Heart,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive, History, Shield as ShieldIcon\n} from 'lucide-react'\nimport { useSettings } from '@/contexts/SettingsContext'\n\nexport default function Settings() {\n  const [activeTab, setActiveTab] = useState('store')\n  const [showPassword, setShowPassword] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const { settings, updateSettings, saveSettings, isLoading, hasUnsavedChanges } = useSettings()\n\n  const tabs = [\n    { id: 'store', label: 'Store Info', icon: Store },\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'security', label: 'Security', icon: Shield },\n    { id: 'appearance', label: 'Appearance', icon: Palette },\n    { id: 'backup', label: 'Backup', icon: Database },\n  ]\n\n  // Helper functions for updating nested settings\n  const updateStoreSettings = (updates: any) => updateSettings('store', updates)\n  const updateProfileSettings = (updates: any) => updateSettings('profile', updates)\n  const updateNotificationSettings = (updates: any) => updateSettings('notifications', updates)\n  const updateSecuritySettings = (updates: any) => updateSettings('security', updates)\n  const updateAppearanceSettings = (updates: any) => updateSettings('appearance', updates)\n  const updateBackupSettings = (updates: any) => updateSettings('backup', updates)\n\n  const handleSave = async () => {\n    try {\n      await saveSettings()\n      alert('Settings saved successfully!')\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      alert('Error saving settings. Please try again.')\n    }\n  }\n\n  const handleExportData = () => {\n    console.log('Exporting data...')\n    alert('Data export started. You will receive an email when ready.')\n  }\n\n  const handleImportData = () => {\n    console.log('Importing data...')\n    alert('Please select a backup file to import.')\n  }\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'logo' | 'avatar') => {\n    const file = event.target.files?.[0]\n    if (file) {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        const result = e.target?.result as string\n        if (type === 'logo') {\n          updateStoreSettings({ \n            branding: { ...settings.store.branding, logo: result } \n          })\n        } else if (type === 'avatar') {\n          updateProfileSettings({ avatar: result })\n        }\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  const addLocation = () => {\n    const newLocation = {\n      id: Date.now(),\n      name: 'New Location',\n      address: '',\n      phone: '',\n      isMain: false,\n    }\n    updateStoreSettings({\n      locations: [...settings.store.locations, newLocation]\n    })\n  }\n\n  const removeLocation = (id: number) => {\n    updateStoreSettings({\n      locations: settings.store.locations.filter(loc => loc.id !== id)\n    })\n  }\n\n  // Professional render functions for all sections\n  const renderProfileSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Personal Information */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <User className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Personal Information\n        </h3>\n\n        <div className=\"flex items-start space-x-6 mb-6\">\n          <div className=\"flex flex-col items-center space-y-3\">\n            <div className=\"w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center bg-gray-50 dark:bg-slate-700 overflow-hidden\">\n              {settings.profile.avatar ? (\n                <img\n                  src={settings.profile.avatar}\n                  alt=\"Profile Avatar\"\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <User className=\"h-12 w-12 text-gray-400\" />\n              )}\n            </div>\n            <div className=\"flex flex-col space-y-2\">\n              <button\n                onClick={() => {\n                  const input = document.createElement('input')\n                  input.type = 'file'\n                  input.accept = 'image/*'\n                  input.onchange = (e) => handleFileUpload(e as any, 'avatar')\n                  input.click()\n                }}\n                className=\"btn-outline text-sm px-3 py-1\"\n              >\n                Upload Photo\n              </button>\n              {settings.profile.avatar && (\n                <button\n                  onClick={() => updateProfileSettings({ avatar: null })}\n                  className=\"text-red-600 hover:text-red-700 text-sm\"\n                >\n                  Remove\n                </button>\n              )}\n            </div>\n          </div>\n\n          <div className=\"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                value={settings.profile.firstName}\n                onChange={(e) => updateProfileSettings({ firstName: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter first name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                value={settings.profile.lastName}\n                onChange={(e) => updateProfileSettings({ lastName: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter last name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address *\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"email\"\n                  value={settings.profile.email}\n                  onChange={(e) => updateProfileSettings({ email: e.target.value })}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Phone Number\n              </label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"tel\"\n                  value={settings.profile.phone}\n                  onChange={(e) => updateProfileSettings({ phone: e.target.value })}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"+63 ************\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Role\n              </label>\n              <select\n                value={settings.profile.role}\n                onChange={(e) => updateProfileSettings({ role: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              >\n                <option value=\"Store Owner\">Store Owner</option>\n                <option value=\"Manager\">Manager</option>\n                <option value=\"Cashier\">Cashier</option>\n                <option value=\"Staff\">Staff</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Date of Birth\n              </label>\n              <input\n                type=\"date\"\n                value={settings.profile.dateOfBirth}\n                onChange={(e) => updateProfileSettings({ dateOfBirth: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Bio\n            </label>\n            <textarea\n              value={settings.profile.bio}\n              onChange={(e) => updateProfileSettings({ bio: e.target.value })}\n              rows={3}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Tell us about yourself...\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Address\n            </label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <textarea\n                value={settings.profile.address}\n                onChange={(e) => updateProfileSettings({ address: e.target.value })}\n                rows={2}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter your address\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Emergency Contact */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <AlertTriangle className=\"h-5 w-5 mr-2 text-red-600\" />\n          Emergency Contact\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Contact Name\n            </label>\n            <input\n              type=\"text\"\n              value={settings.profile.emergencyContact.name}\n              onChange={(e) => updateProfileSettings({\n                emergencyContact: { ...settings.profile.emergencyContact, name: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Emergency contact name\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Phone Number\n            </label>\n            <div className=\"relative\">\n              <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.profile.emergencyContact.phone}\n                onChange={(e) => updateProfileSettings({\n                  emergencyContact: { ...settings.profile.emergencyContact, phone: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Relationship\n            </label>\n            <select\n              value={settings.profile.emergencyContact.relationship}\n              onChange={(e) => updateProfileSettings({\n                emergencyContact: { ...settings.profile.emergencyContact, relationship: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"Family\">Family</option>\n              <option value=\"Friend\">Friend</option>\n              <option value=\"Colleague\">Colleague</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Basic Notification Preferences */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Bell className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Alert Preferences\n        </h3>\n        <div className=\"space-y-4\">\n          {Object.entries(settings.notifications).filter(([key]) =>\n            !['channels', 'customRules', 'templates'].includes(key)\n          ).map(([key, value]) => (\n            <div key={key} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <div className=\"flex-1\">\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                </label>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                  {key === 'lowStock' && 'Get notified when products are running low'}\n                  {key === 'newDebt' && 'Alert when new customer debt is recorded'}\n                  {key === 'paymentReceived' && 'Notification for debt payments'}\n                  {key === 'dailyReport' && 'Daily business summary report'}\n                  {key === 'weeklyReport' && 'Weekly business analytics report'}\n                  {key === 'emailNotifications' && 'Receive notifications via email'}\n                  {key === 'smsNotifications' && 'Receive notifications via SMS'}\n                  {key === 'pushNotifications' && 'Receive push notifications in browser'}\n                </p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer ml-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={value as boolean}\n                  onChange={(e) => updateNotificationSettings({ [key]: e.target.checked })}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Notification Channels */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Mail className=\"h-5 w-5 mr-2 text-green-600\" />\n          Delivery Channels\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Email Address\n            </label>\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"email\"\n                value={settings.notifications.channels.email}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, email: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              SMS Number\n            </label>\n            <div className=\"relative\">\n              <Smartphone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.notifications.channels.sms}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, sms: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Webhook URL (Optional)\n            </label>\n            <div className=\"relative\">\n              <Globe className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"url\"\n                value={settings.notifications.channels.webhook}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, webhook: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"https://your-webhook-url.com/notifications\"\n              />\n            </div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Send notifications to external systems via webhook\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Custom Notification Rules */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Zap className=\"h-5 w-5 mr-2 text-yellow-600\" />\n          Custom Rules\n        </h3>\n        <div className=\"space-y-4\">\n          {settings.notifications.customRules.map((rule) => (\n            <div key={rule.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{rule.name}</h4>\n                  <span className={`px-2 py-1 text-xs rounded-full ${\n                    rule.enabled\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n                  }`}>\n                    {rule.enabled ? 'Active' : 'Inactive'}\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <button className=\"text-blue-600 hover:text-blue-700 p-1\">\n                    <Edit3 className=\"h-4 w-4\" />\n                  </button>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={rule.enabled}\n                      onChange={(e) => {\n                        const updatedRules = settings.notifications.customRules.map(r =>\n                          r.id === rule.id ? { ...r, enabled: e.target.checked } : r\n                        )\n                        updateNotificationSettings({ customRules: updatedRules })\n                      }}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n                  </label>\n                </div>\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                <p><strong>Condition:</strong> {rule.condition}</p>\n                <p><strong>Action:</strong> {rule.action}</p>\n              </div>\n            </div>\n          ))}\n          <button className=\"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2\">\n            <Plus className=\"h-5 w-5\" />\n            <span>Add Custom Rule</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderSecuritySettings = () => (\n    <div className=\"space-y-8\">\n      {/* Password Management */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Key className=\"h-5 w-5 mr-2 text-red-600\" />\n          Password Management\n        </h3>\n\n        <div className=\"grid grid-cols-1 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Current Password\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.currentPassword}\n                onChange={(e) => updateSecuritySettings({ currentPassword: e.target.value })}\n                className=\"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter current password\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-3 text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n              </button>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                New Password\n              </label>\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.newPassword}\n                onChange={(e) => updateSecuritySettings({ newPassword: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter new password\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Confirm New Password\n              </label>\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.confirmPassword}\n                onChange={(e) => updateSecuritySettings({ confirmPassword: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Confirm new password\"\n              />\n            </div>\n          </div>\n\n          <button className=\"btn-primary w-fit\">\n            Update Password\n          </button>\n        </div>\n      </div>\n\n      {/* Two-Factor Authentication */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Smartphone className=\"h-5 w-5 mr-2 text-green-600\" />\n          Two-Factor Authentication\n        </h3>\n\n        <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">Enable 2FA</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Add an extra layer of security to your account\n            </p>\n          </div>\n          <label className=\"relative inline-flex items-center cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.security.twoFactorAuth}\n              onChange={(e) => updateSecuritySettings({ twoFactorAuth: e.target.checked })}\n              className=\"sr-only peer\"\n            />\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n          </label>\n        </div>\n\n        {settings.security.twoFactorAuth && (\n          <div className=\"mt-4 p-4 border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Check className=\"h-5 w-5 text-green-600\" />\n              <span className=\"font-medium text-green-800 dark:text-green-300\">2FA is enabled</span>\n            </div>\n            <div className=\"space-y-3\">\n              <button className=\"btn-outline text-sm\">\n                View Recovery Codes\n              </button>\n              <button className=\"btn-outline text-sm text-red-600 border-red-300 hover:bg-red-50\">\n                Disable 2FA\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Session Management */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Clock className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Session Management\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Session Timeout (minutes)\n            </label>\n            <select\n              value={settings.security.sessionTimeout}\n              onChange={(e) => updateSecuritySettings({ sessionTimeout: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"15\">15 minutes</option>\n              <option value=\"30\">30 minutes</option>\n              <option value=\"60\">1 hour</option>\n              <option value=\"120\">2 hours</option>\n              <option value=\"480\">8 hours</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Password Expiry (days)\n            </label>\n            <select\n              value={settings.security.passwordExpiry}\n              onChange={(e) => updateSecuritySettings({ passwordExpiry: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"30\">30 days</option>\n              <option value=\"60\">60 days</option>\n              <option value=\"90\">90 days</option>\n              <option value=\"180\">180 days</option>\n              <option value=\"365\">1 year</option>\n              <option value=\"0\">Never</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Max Login Attempts\n            </label>\n            <select\n              value={settings.security.loginAttempts}\n              onChange={(e) => updateSecuritySettings({ loginAttempts: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"3\">3 attempts</option>\n              <option value=\"5\">5 attempts</option>\n              <option value=\"10\">10 attempts</option>\n              <option value=\"0\">Unlimited</option>\n            </select>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderAppearanceSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Theme Selection */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Monitor className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Theme Selection\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {[\n            { id: 'light', name: 'Light', icon: Sun, preview: 'bg-white border-gray-200' },\n            { id: 'dark', name: 'Dark', icon: Moon, preview: 'bg-slate-800 border-slate-600' },\n            { id: 'auto', name: 'Auto', icon: Monitor, preview: 'bg-gradient-to-r from-white to-slate-800 border-gray-400' }\n          ].map((theme) => {\n            const Icon = theme.icon\n            return (\n              <label key={theme.id} className=\"cursor-pointer\">\n                <input\n                  type=\"radio\"\n                  name=\"theme\"\n                  value={theme.id}\n                  checked={settings.appearance.theme === theme.id}\n                  onChange={(e) => updateAppearanceSettings({ theme: e.target.value })}\n                  className=\"sr-only\"\n                />\n                <div className={`p-4 border-2 rounded-lg transition-all ${\n                  settings.appearance.theme === theme.id\n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                }`}>\n                  <div className={`w-full h-20 rounded-md mb-3 border ${theme.preview}`}></div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Icon className=\"h-4 w-4\" />\n                    <span className=\"font-medium\">{theme.name}</span>\n                  </div>\n                </div>\n              </label>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Color Scheme Customization */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <PaletteIcon className=\"h-5 w-5 mr-2 text-pink-600\" />\n          Color Scheme\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {Object.entries(settings.appearance.colorScheme).map(([key, color]) => (\n            <div key={key}>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize\">\n                {key} Color\n              </label>\n              <div className=\"flex items-center space-x-3\">\n                <input\n                  type=\"color\"\n                  value={color}\n                  onChange={(e) => updateAppearanceSettings({\n                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }\n                  })}\n                  className=\"w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer\"\n                />\n                <input\n                  type=\"text\"\n                  value={color}\n                  onChange={(e) => updateAppearanceSettings({\n                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }\n                  })}\n                  className=\"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"#000000\"\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-6 flex space-x-3\">\n          <button className=\"btn-outline\">\n            Reset to Default\n          </button>\n          <button className=\"btn-outline\">\n            Preview Changes\n          </button>\n        </div>\n      </div>\n\n      {/* Layout Preferences */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Layout className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Layout Preferences\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Sidebar Position\n            </label>\n            <select\n              value={settings.appearance.layout.sidebarPosition}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, sidebarPosition: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"left\">Left</option>\n              <option value=\"right\">Right</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              UI Density\n            </label>\n            <select\n              value={settings.appearance.layout.density}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, density: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"compact\">Compact</option>\n              <option value=\"comfortable\">Comfortable</option>\n              <option value=\"spacious\">Spacious</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"mt-6 space-y-4\">\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.appearance.layout.showAnimations}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, showAnimations: e.target.checked }\n              })}\n              className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">Enable animations</span>\n          </label>\n\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.appearance.layout.compactMode}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, compactMode: e.target.checked }\n              })}\n              className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">Compact mode</span>\n          </label>\n        </div>\n      </div>\n\n      {/* Typography Settings */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Type className=\"h-5 w-5 mr-2 text-green-600\" />\n          Typography\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Family\n            </label>\n            <select\n              value={settings.appearance.typography.fontFamily}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontFamily: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"Inter\">Inter</option>\n              <option value=\"Roboto\">Roboto</option>\n              <option value=\"Open Sans\">Open Sans</option>\n              <option value=\"Poppins\">Poppins</option>\n              <option value=\"Lato\">Lato</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Size\n            </label>\n            <select\n              value={settings.appearance.typography.fontSize}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontSize: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"small\">Small</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"large\">Large</option>\n              <option value=\"extra-large\">Extra Large</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Weight\n            </label>\n            <select\n              value={settings.appearance.typography.fontWeight}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontWeight: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"light\">Light</option>\n              <option value=\"normal\">Normal</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"semibold\">Semibold</option>\n              <option value=\"bold\">Bold</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Preview</h4>\n          <div\n            className=\"text-gray-700 dark:text-gray-300\"\n            style={{\n              fontFamily: settings.appearance.typography.fontFamily,\n              fontSize: settings.appearance.typography.fontSize === 'small' ? '14px' :\n                        settings.appearance.typography.fontSize === 'medium' ? '16px' :\n                        settings.appearance.typography.fontSize === 'large' ? '18px' : '20px',\n              fontWeight: settings.appearance.typography.fontWeight\n            }}\n          >\n            The quick brown fox jumps over the lazy dog. This is how your text will appear with the selected typography settings.\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderBackupSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Backup Configuration */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Database className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Backup Configuration\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Auto Backup\n            </label>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={settings.backup.autoBackup}\n                onChange={(e) => updateBackupSettings({ autoBackup: e.target.checked })}\n                className=\"sr-only peer\"\n              />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n              <span className=\"ml-3 text-sm text-gray-700 dark:text-gray-300\">\n                Enable automatic backups\n              </span>\n            </label>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Backup Frequency\n            </label>\n            <select\n              value={settings.backup.backupFrequency}\n              onChange={(e) => updateBackupSettings({ backupFrequency: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              disabled={!settings.backup.autoBackup}\n            >\n              <option value=\"hourly\">Every Hour</option>\n              <option value=\"daily\">Daily</option>\n              <option value=\"weekly\">Weekly</option>\n              <option value=\"monthly\">Monthly</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Retention Period (Days)\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"365\"\n              value={settings.backup.retentionDays}\n              onChange={(e) => updateBackupSettings({ retentionDays: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Next Backup\n            </label>\n            <div className=\"p-3 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {settings.backup.autoBackup ? 'Tomorrow at 2:00 AM' : 'Manual backup only'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Cloud Storage Integration */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Cloud className=\"h-5 w-5 mr-2 text-green-600\" />\n          Cloud Storage\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Storage Provider\n            </label>\n            <select\n              value={settings.backup.cloudStorage.provider}\n              onChange={(e) => updateBackupSettings({\n                cloudStorage: { ...settings.backup.cloudStorage, provider: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"local\">Local Storage</option>\n              <option value=\"aws\">Amazon S3</option>\n              <option value=\"google\">Google Cloud</option>\n              <option value=\"azure\">Azure Blob</option>\n              <option value=\"dropbox\">Dropbox</option>\n            </select>\n          </div>\n\n          {settings.backup.cloudStorage.provider !== 'local' && (\n            <>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Bucket/Container Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.backup.cloudStorage.bucket}\n                  onChange={(e) => updateBackupSettings({\n                    cloudStorage: { ...settings.backup.cloudStorage, bucket: e.target.value }\n                  })}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"my-backup-bucket\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Access Key\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.backup.cloudStorage.accessKey}\n                  onChange={(e) => updateBackupSettings({\n                    cloudStorage: { ...settings.backup.cloudStorage, accessKey: e.target.value }\n                  })}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"Your access key\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Secret Key\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    value={settings.backup.cloudStorage.secretKey}\n                    onChange={(e) => updateBackupSettings({\n                      cloudStorage: { ...settings.backup.cloudStorage, secretKey: e.target.value }\n                    })}\n                    className=\"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                    placeholder=\"Your secret key\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-3 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                  </button>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {settings.backup.cloudStorage.provider !== 'local' && (\n          <div className=\"mt-4 flex space-x-3\">\n            <button className=\"btn-outline\">\n              Test Connection\n            </button>\n            <button className=\"btn-primary\">\n              Save Configuration\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Backup History */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Archive className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Backup History\n        </h3>\n\n        <div className=\"space-y-3\">\n          {settings.backup.backupHistory.map((backup) => (\n            <div key={backup.id} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <div className={`w-3 h-3 rounded-full ${\n                  backup.status === 'completed' ? 'bg-green-500' :\n                  backup.status === 'failed' ? 'bg-red-500' : 'bg-yellow-500'\n                }`}></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {backup.type.charAt(0).toUpperCase() + backup.type.slice(1)} Backup\n                  </p>\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {new Date(backup.timestamp).toLocaleString()} • {backup.size}\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <button className=\"text-blue-600 hover:text-blue-700 p-1\" title=\"Download\">\n                  <Download className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-green-600 hover:text-green-700 p-1\" title=\"Restore\">\n                  <RefreshCw className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-red-600 hover:text-red-700 p-1\" title=\"Delete\">\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-4 flex justify-between items-center\">\n          <button className=\"text-sm text-blue-600 hover:text-blue-700\">\n            View All Backups\n          </button>\n          <button className=\"text-sm text-red-600 hover:text-red-700\">\n            Clear Old Backups\n          </button>\n        </div>\n      </div>\n\n      {/* Manual Backup & Restore */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <HardDrive className=\"h-5 w-5 mr-2 text-indigo-600\" />\n          Manual Operations\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button\n            onClick={handleExportData}\n            className=\"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\"\n          >\n            <Download className=\"h-5 w-5 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900 dark:text-white\">Create Backup</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Export all data now</p>\n            </div>\n          </button>\n\n          <button\n            onClick={handleImportData}\n            className=\"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\"\n          >\n            <Upload className=\"h-5 w-5 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900 dark:text-white\">Restore Data</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Import from backup</p>\n            </div>\n          </button>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg\">\n          <div className=\"flex items-start space-x-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-yellow-800 dark:text-yellow-300\">Important</h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-400 mt-1\">\n                Always verify your backups before relying on them. Test restore procedures regularly to ensure data integrity.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Last Backup</p>\n              <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                {new Date(settings.backup.lastBackup).toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Status</p>\n              <div className=\"flex items-center\">\n                <Check className=\"h-4 w-4 text-green-600 mr-1\" />\n                <span className=\"text-xs text-green-600\">Completed</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'store':\n        return renderStoreSettings()\n      case 'profile':\n        return renderProfileSettings()\n      case 'notifications':\n        return renderNotificationSettings()\n      case 'security':\n        return renderSecuritySettings()\n      case 'appearance':\n        return renderAppearanceSettings()\n      case 'backup':\n        return renderBackupSettings()\n      default:\n        return (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings coming soon...\n            </p>\n          </div>\n        )\n    }\n  }\n\n  // Store Settings Component\n  const renderStoreSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Basic Store Information */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Store className=\"h-5 w-5 mr-2 text-green-600\" />\n          Basic Information\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Name *\n            </label>\n            <input\n              type=\"text\"\n              value={settings.store.name}\n              onChange={(e) => updateStoreSettings({ name: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Enter store name\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Website\n            </label>\n            <div className=\"relative\">\n              <Globe className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"url\"\n                value={settings.store.website}\n                onChange={(e) => updateStoreSettings({ website: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"https://yourstore.com\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Phone Number *\n            </label>\n            <div className=\"relative\">\n              <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.store.phone}\n                onChange={(e) => updateStoreSettings({ phone: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Email Address *\n            </label>\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"email\"\n                value={settings.store.email}\n                onChange={(e) => updateStoreSettings({ email: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"mt-6\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Address *\n          </label>\n          <div className=\"relative\">\n            <MapPin className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n            <textarea\n              value={settings.store.address}\n              onChange={(e) => updateStoreSettings({ address: e.target.value })}\n              rows={3}\n              className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Enter complete store address\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Business Hours & Operating Days */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Clock className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Business Hours & Operating Days\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Opening Time\n            </label>\n            <input\n              type=\"time\"\n              value={settings.store.businessHours.open}\n              onChange={(e) => updateStoreSettings({ \n                businessHours: { ...settings.store.businessHours, open: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Closing Time\n            </label>\n            <input\n              type=\"time\"\n              value={settings.store.businessHours.close}\n              onChange={(e) => updateStoreSettings({ \n                businessHours: { ...settings.store.businessHours, close: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            Operating Days\n          </label>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3\">\n            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (\n              <label key={day} className=\"flex items-center space-x-2 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.store.operatingDays.includes(day)}\n                  onChange={(e) => {\n                    const updatedDays = e.target.checked\n                      ? [...settings.store.operatingDays, day]\n                      : settings.store.operatingDays.filter(d => d !== day)\n                    updateStoreSettings({ operatingDays: updatedDays })\n                  }}\n                  className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300 capitalize\">\n                  {day.slice(0, 3)}\n                </span>\n              </label>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Store Branding */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <PaletteIcon className=\"h-5 w-5 mr-2 text-pink-600\" />\n          Store Branding\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Logo\n            </label>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-20 h-20 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-slate-700\">\n                {settings.store.branding.logo ? (\n                  <img\n                    src={settings.store.branding.logo}\n                    alt=\"Store Logo\"\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Camera className=\"h-8 w-8 text-gray-400\" />\n                )}\n              </div>\n              <div className=\"flex flex-col space-y-2\">\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"btn-outline text-sm px-4 py-2\"\n                >\n                  Upload Logo\n                </button>\n                {settings.store.branding.logo && (\n                  <button\n                    onClick={() => updateStoreSettings({\n                      branding: { ...settings.store.branding, logo: null }\n                    })}\n                    className=\"text-red-600 hover:text-red-700 text-sm\"\n                  >\n                    Remove\n                  </button>\n                )}\n              </div>\n            </div>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={(e) => handleFileUpload(e, 'logo')}\n              className=\"hidden\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Slogan\n            </label>\n            <input\n              type=\"text\"\n              value={settings.store.branding.slogan}\n              onChange={(e) => updateStoreSettings({\n                branding: { ...settings.store.branding, slogan: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Your Neighborhood Store\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Settings</h2>\n        <div className=\"flex items-center space-x-3\">\n          {hasUnsavedChanges && (\n            <span className=\"text-sm text-yellow-600 dark:text-yellow-400 flex items-center\">\n              <AlertTriangle className=\"h-4 w-4 mr-1\" />\n              Unsaved changes\n            </span>\n          )}\n          <button\n            onClick={handleSave}\n            disabled={isLoading || !hasUnsavedChanges}\n            className=\"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n            ) : (\n              <Save className=\"h-4 w-4 mr-2\" />\n            )}\n            {isLoading ? 'Saving...' : 'Save Changes'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-green-500 text-green-600 dark:text-green-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5 mr-2\" />\n                  {tab.label}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {renderTabContent()}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAXA;;;;AAae,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE3F,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,OAAO;YAAc,MAAM,uMAAA,CAAA,QAAK;QAAC;QAChD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,2MAAA,CAAA,UAAO;QAAC;QACvD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACjD;IAED,gDAAgD;IAChD,MAAM,sBAAsB,CAAC,UAAiB,eAAe,SAAS;IACtE,MAAM,wBAAwB,CAAC,UAAiB,eAAe,WAAW;IAC1E,MAAM,6BAA6B,CAAC,UAAiB,eAAe,iBAAiB;IACrF,MAAM,yBAAyB,CAAC,UAAiB,eAAe,YAAY;IAC5E,MAAM,2BAA2B,CAAC,UAAiB,eAAe,cAAc;IAChF,MAAM,uBAAuB,CAAC,UAAiB,eAAe,UAAU;IAExE,MAAM,aAAa;QACjB,IAAI;YACF,MAAM;YACN,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,MAAM,mBAAmB,CAAC,OAA4C;QACpE,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,IAAI,SAAS,QAAQ;oBACnB,oBAAoB;wBAClB,UAAU;4BAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;4BAAE,MAAM;wBAAO;oBACvD;gBACF,OAAO,IAAI,SAAS,UAAU;oBAC5B,sBAAsB;wBAAE,QAAQ;oBAAO;gBACzC;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG;YACZ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA,oBAAoB;YAClB,WAAW;mBAAI,SAAS,KAAK,CAAC,SAAS;gBAAE;aAAY;QACvD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;YAClB,WAAW,SAAS,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/D;IACF;IAEA,iDAAiD;IACjD,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,MAAM,iBACtB,6LAAC;gDACC,KAAK,SAAS,OAAO,CAAC,MAAM;gDAC5B,KAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;wDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;wDACrC,MAAM,IAAI,GAAG;wDACb,MAAM,MAAM,GAAG;wDACf,MAAM,QAAQ,GAAG,CAAC,IAAM,iBAAiB,GAAU;wDACnD,MAAM,KAAK;oDACb;oDACA,WAAU;8DACX;;;;;;gDAGA,SAAS,OAAO,CAAC,MAAM,kBACtB,6LAAC;oDACC,SAAS,IAAM,sBAAsB;4DAAE,QAAQ;wDAAK;oDACpD,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,SAAS;oDACjC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACnE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,QAAQ;oDAChC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,CAAC,KAAK;4DAC7B,UAAU,CAAC,IAAM,sBAAsB;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,CAAC,KAAK;4DAC7B,UAAU,CAAC,IAAM,sBAAsB;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,OAAO,SAAS,OAAO,CAAC,IAAI;oDAC5B,UAAU,CAAC,IAAM,sBAAsB;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC9D,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,WAAW;oDACnC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACrE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,OAAO,CAAC,GAAG;4CAC3B,UAAU,CAAC,IAAM,sBAAsB;oDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACC,OAAO,SAAS,OAAO,CAAC,OAAO;oDAC/B,UAAU,CAAC,IAAM,sBAAsB;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,IAAI;4CAC7C,UAAU,CAAC,IAAM,sBAAsB;oDACrC,kBAAkB;wDAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjF;4CACA,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,KAAK;oDAC9C,UAAU,CAAC,IAAM,sBAAsB;4DACrC,kBAAkB;gEAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClF;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,YAAY;4CACrD,UAAU,CAAC,IAAM,sBAAsB;oDACrC,kBAAkB;wDAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzF;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlC,MAAM,6BAA6B,kBACjC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAGjD,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,SAAS,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,GACnD,CAAC;oCAAC;oCAAY;oCAAe;iCAAY,CAAC,QAAQ,CAAC,MACnD,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjB,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DACd,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;8DAEtE,6LAAC;oDAAE,WAAU;;wDACV,QAAQ,cAAc;wDACtB,QAAQ,aAAa;wDACrB,QAAQ,qBAAqB;wDAC7B,QAAQ,iBAAiB;wDACzB,QAAQ,kBAAkB;wDAC1B,QAAQ,wBAAwB;wDAChC,QAAQ,sBAAsB;wDAC9B,QAAQ,uBAAuB;;;;;;;;;;;;;sDAGpC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,2BAA2B;4DAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACtE,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAvBT;;;;;;;;;;;;;;;;8BA+BhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,KAAK;oDAC5C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,GAAG;oDAC1C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACtE;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,OAAO;oDAC9C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;;;;;;;;8BAQnE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,qBACvC,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA6C,KAAK,IAAI;;;;;;0EACpE,6LAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,OAAO,GACR,sEACA,iEACJ;0EACC,KAAK,OAAO,GAAG,WAAW;;;;;;;;;;;;kEAG/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC;gEAAM,WAAU;;kFACf,6LAAC;wEACC,MAAK;wEACL,SAAS,KAAK,OAAO;wEACrB,UAAU,CAAC;4EACT,MAAM,eAAe,SAAS,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,IAC1D,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;oFAAE,GAAG,CAAC;oFAAE,SAAS,EAAE,MAAM,CAAC,OAAO;gFAAC,IAAI;4EAE3D,2BAA2B;gFAAE,aAAa;4EAAa;wEACzD;wEACA,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EAAE,6LAAC;0EAAO;;;;;;4DAAmB;4DAAE,KAAK,SAAS;;;;;;;kEAC9C,6LAAC;;0EAAE,6LAAC;0EAAO;;;;;;4DAAgB;4DAAE,KAAK,MAAM;;;;;;;;;;;;;;uCAlClC,KAAK,EAAE;;;;;8CAsCnB,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhB,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,eAAe;oDACxC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,WAAW;oDACpC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,eAAe;oDACxC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAO,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;8BAO1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,iNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;8CAI1D,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,SAAS,QAAQ,CAAC,aAAa;4CACxC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,eAAe,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC1E,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;wBAIlB,SAAS,QAAQ,CAAC,aAAa,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;8CAEnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAsB;;;;;;sDAGxC,6LAAC;4CAAO,WAAU;sDAAkE;;;;;;;;;;;;;;;;;;;;;;;;8BAS5F,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,cAAc;4CACvC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAIxB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,cAAc;4CACvC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;8CAItB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,aAAa;4CACtC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9B,MAAM,2BAA2B,kBAC/B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAS,MAAM;oCAAS,MAAM,mMAAA,CAAA,MAAG;oCAAE,SAAS;gCAA2B;gCAC7E;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,MAAM,qMAAA,CAAA,OAAI;oCAAE,SAAS;gCAAgC;gCACjF;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,MAAM,2MAAA,CAAA,UAAO;oCAAE,SAAS;gCAA2D;6BAChH,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,MAAM,IAAI;gCACvB,qBACE,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,MAAM,EAAE;4CACf,SAAS,SAAS,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE;4CAC/C,UAAU,CAAC,IAAM,yBAAyB;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAW,CAAC,uCAAuC,EACtD,SAAS,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,GAClC,sDACA,8DACJ;;8DACA,6LAAC;oDAAI,WAAW,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;;;;;;8DACrE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAe,MAAM,IAAI;;;;;;;;;;;;;;;;;;;mCAjBnC,MAAM,EAAE;;;;;4BAsBxB;;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,2MAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd;gDAAI;;;;;;;sDAEP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,yBAAyB;4DACxC,aAAa;gEAAE,GAAG,SAAS,UAAU,CAAC,WAAW;gEAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3E;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,yBAAyB;4DACxC,aAAa;gEAAE,GAAG,SAAS,UAAU,CAAC,WAAW;gEAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;mCApBR;;;;;;;;;;sCA2Bd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,6LAAC;oCAAO,WAAU;8CAAc;;;;;;;;;;;;;;;;;;8BAOpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,wNAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU,CAAC,MAAM,CAAC,eAAe;4CACjD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC3E;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI1B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU,CAAC,MAAM,CAAC,OAAO;4CACzC,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,6LAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,SAAS,UAAU,CAAC,MAAM,CAAC,cAAc;4CAClD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;oDAAC;gDAC5E;4CACA,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;8CAG7D,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,SAAS,UAAU,CAAC,MAAM,CAAC,WAAW;4CAC/C,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC;gDACzE;4CACA,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;;8BAMjE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAIlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;4CAChD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9E;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAIzB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ;4CAC9C,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5E;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAc;;;;;;;;;;;;;;;;;;8CAIhC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;4CAChD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9E;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;wCACrD,UAAU,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,UAAU,SACtD,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,WAAW,SACvD,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,UAAU,SAAS;wCACzE,YAAY,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;oCACvD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;IAQT,MAAM,uBAAuB,kBAC3B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,SAAS,MAAM,CAAC,UAAU;oDACnC,UAAU,CAAC,IAAM,qBAAqB;4DAAE,YAAY,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACrE,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;8CAMpE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,MAAM,CAAC,eAAe;4CACtC,UAAU,CAAC,IAAM,qBAAqB;oDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;4CACV,UAAU,CAAC,SAAS,MAAM,CAAC,UAAU;;8DAErC,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,MAAM,CAAC,aAAa;4CACpC,UAAU,CAAC,IAAM,qBAAqB;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,UAAU,GAAG,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAInD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ;4CAC5C,UAAU,CAAC,IAAM,qBAAqB;oDACpC,cAAc;wDAAE,GAAG,SAAS,MAAM,CAAC,YAAY;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5E;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;gCAI3B,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ,KAAK,yBACzC;;sDACE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,MAAM;oDAC1C,UAAU,CAAC,IAAM,qBAAqB;4DACpC,cAAc;gEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS;oDAC7C,UAAU,CAAC,IAAM,qBAAqB;4DACpC,cAAc;gEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC7E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAM,eAAe,SAAS;4DAC9B,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS;4DAC7C,UAAU,CAAC,IAAM,qBAAqB;oEACpC,cAAc;wEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC7E;4DACA,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;sEAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAAe,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ3E,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ,KAAK,yBACzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,6LAAC;oCAAO,WAAU;8CAAc;;;;;;;;;;;;;;;;;;8BAQtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAItD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,uBAClC,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,MAAM,KAAK,cAAc,iBAChC,OAAO,MAAM,KAAK,WAAW,eAAe,iBAC5C;;;;;;8DACF,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;;gEACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;gEAAG;;;;;;;sEAE9D,6LAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;gEAAG;gEAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;;sDAIlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;oDAAwC,OAAM;8DAC9D,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAO,WAAU;oDAA0C,OAAM;8DAChE,cAAA,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;oDAAO,WAAU;oDAAsC,OAAM;8DAC5D,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCAvBd,OAAO,EAAE;;;;;;;;;;sCA8BvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAA4C;;;;;;8CAG9D,6LAAC;oCAAO,WAAU;8CAA0C;;;;;;;;;;;;;;;;;;8BAOhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;8CAI5D,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;sCAK9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;sCAOvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,SAAS,MAAM,CAAC,UAAU,EAAE,cAAc;;;;;;;;;;;;kDAGxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASvD,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BACV,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;4BAAG;;;;;;;;;;;;QAIlE;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,IAAI;4CAC1B,UAAU,CAAC,IAAM,oBAAoB;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5D,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,OAAO;oDAC7B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC/D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,KAAK;oDAC3B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC7D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,KAAK;oDAC3B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC7D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,OAAO,SAAS,KAAK,CAAC,OAAO;4CAC7B,UAAU,CAAC,IAAM,oBAAoB;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,IAAI;4CACxC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,eAAe;wDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzE;4CACA,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,KAAK;4CACzC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,eAAe;wDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC1E;4CACA,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAU;wCAAW;wCAAa;wCAAY;wCAAU;wCAAY;qCAAS,CAAC,GAAG,CAAC,CAAC,oBACnF,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDACC,MAAK;oDACL,SAAS,SAAS,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC;oDAC/C,UAAU,CAAC;wDACT,MAAM,cAAc,EAAE,MAAM,CAAC,OAAO,GAChC;+DAAI,SAAS,KAAK,CAAC,aAAa;4DAAE;yDAAI,GACtC,SAAS,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,oBAAoB;4DAAE,eAAe;wDAAY;oDACnD;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,CAAC,GAAG;;;;;;;2CAbN;;;;;;;;;;;;;;;;;;;;;;8BAsBpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,2MAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI,iBAC3B,6LAAC;wDACC,KAAK,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI;wDACjC,KAAI;wDACJ,WAAU;;;;;6EAGZ,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,aAAa,OAAO,EAAE;4DACrC,WAAU;sEACX;;;;;;wDAGA,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI,kBAC3B,6LAAC;4DACC,SAAS,IAAM,oBAAoB;oEACjC,UAAU;wEAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;wEAAE,MAAM;oEAAK;gEACrD;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAMP,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,GAAG;4CACrC,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,QAAQ,CAAC,MAAM;4CACrC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,UAAU;wDAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE;4CACA,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,6LAAC;wBAAI,WAAU;;4BACZ,mCACC,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAI9C,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,CAAC;gCACxB,WAAU;;oCAET,0BACC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAEjB,YAAY,cAAc;;;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC;gCACT,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,6EAA6E,EACvF,cAAc,IAAI,EAAE,GAChB,wDACA,0HACJ;;sDAEF,6LAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;GAn8CwB;;QAI2D,sIAAA,CAAA,cAAW;;;KAJtE", "debugId": null}}, {"offset": {"line": 14567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  color?: 'primary' | 'secondary' | 'white'\n  text?: string\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'primary', \n  text \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  }\n\n  const colorClasses = {\n    primary: 'border-green-500 border-t-transparent',\n    secondary: 'border-yellow-400 border-t-transparent',\n    white: 'border-white border-t-transparent'\n  }\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-3\">\n      <motion.div\n        className={`${sizeClasses[size]} border-2 ${colorClasses[color]} rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n      {text && (\n        <motion.p\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n          className=\"text-sm text-gray-600 dark:text-gray-400\"\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACgB;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;gBAC9E,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;YAED,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET;;;;;;;;;;;;AAKX;KAxCwB", "debugId": null}}, {"offset": {"line": 14640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\">\n            <span className=\"text-white font-bold text-2xl\">R</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Loading Revantad Store\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Please wait while we prepare your dashboard...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-red-600 dark:text-red-400 font-bold text-2xl\">!</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Redirecting to login page...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAoD;;;;;;;;;;;kCAEtE,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBAAO;kBAAG;;AACZ;GA/CwB;;QACiB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 14788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/index.ts"], "sourcesContent": ["// Component exports for better organization and cleaner imports\n// This file serves as a central export point for all components\n\n// Layout Components\nexport { default as AdminHeader } from './AdminHeader'\nexport { default as Sidebar } from './Sidebar'\n\n// Dashboard Components\nexport { default as DashboardStats } from './DashboardStats'\nexport { default as APIGraphing } from './APIGraphing'\n\n// Product Management Components\nexport { default as ProductsSection } from './ProductsSection'\nexport { default as ProductModal } from './ProductModal'\n\n// Debt Management Components\nexport { default as DebtsSection } from './DebtsSection'\nexport { default as DebtModal } from './DebtModal'\n\n// Feature Components\nexport { default as FamilyGallery } from './FamilyGallery'\nexport { default as Calendar } from './Calendar'\nexport { default as History } from './History'\nexport { default as Settings } from './Settings'\n\n// Utility Components\nexport { default as LoadingSpinner } from './LoadingSpinner'\nexport { default as ProtectedRoute } from './ProtectedRoute'\nexport { ThemeProvider } from './ThemeProvider'\n\n// Re-export commonly used types\nexport type { Product, CustomerDebt } from '@/lib/supabase'\n"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,gEAAgE;AAEhE,oBAAoB;;AACpB;AACA;AAEA,uBAAuB;AACvB;AACA;AAEA,gCAAgC;AAChC;AACA;AAEA,6BAA6B;AAC7B;AACA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AAEA,qBAAqB;AACrB;AACA;AACA", "debugId": null}}]}