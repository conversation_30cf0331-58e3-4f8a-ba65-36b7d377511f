'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import ReactECharts from 'echarts-for-react'
import {
  TrendingUp,
  DollarSign,
  Package,
  Users,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  BarChart3,
  LineChart,
  PieChart,
  Activity,
  Eye,
  Settings,
  Maximize2,
  ChevronDown,
  Search,
  X,
  FileText,
  Image,
  FileSpreadsheet
} from 'lucide-react'
import { useTheme } from 'next-themes'

import type { DashboardStats } from '@/types'

interface APIGraphingProps {
  stats: DashboardStats
}

interface ChartData {
  salesData: number[]
  debtData: number[]
  categoryData: { value: number; name: string; itemStyle: { color: string } }[]
  trendData: number[]
  hourlyData: number[]
  weeklyData: number[]
}

interface FilterState {
  dateRange: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'
  category: string
  chartType: 'line' | 'bar' | 'area'
  customDateFrom?: string
  customDateTo?: string
}

export default function APIGraphing({ stats }: APIGraphingProps) {
  const { resolvedTheme } = useTheme()
  const [chartData, setChartData] = useState<ChartData>({
    salesData: [],
    debtData: [],
    categoryData: [],
    trendData: [],
    hourlyData: [],
    weeklyData: []
  })
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [activeChart, setActiveChart] = useState<'sales' | 'debt' | 'category' | 'trend'>('sales')
  const [filters, setFilters] = useState<FilterState>({
    dateRange: 'month',
    category: 'all',
    chartType: 'line'
  })
  const [showFilters, setShowFilters] = useState(false)
  const [showExportMenu, setShowExportMenu] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Advanced data generation with realistic patterns
  const generateAdvancedData = useCallback(() => {
    setIsLoading(true)

    setTimeout(() => {
      // Generate sales data with seasonal patterns
      const salesData = Array.from({ length: 12 }, (_, i) => {
        const baseValue = 30000
        const seasonalFactor = Math.sin((i / 12) * 2 * Math.PI) * 10000
        const randomFactor = (Math.random() - 0.5) * 8000
        return Math.max(15000, Math.floor(baseValue + seasonalFactor + randomFactor))
      })

      // Generate debt data with weekly patterns
      const debtData = Array.from({ length: 7 }, (_, i) => {
        const baseValue = 8000
        const weekendFactor = (i === 0 || i === 6) ? -2000 : 0
        const randomFactor = (Math.random() - 0.5) * 3000
        return Math.max(3000, Math.floor(baseValue + weekendFactor + randomFactor))
      })

      // Generate category data
      const categoryData = [
        { value: 35, name: 'Snacks', itemStyle: { color: '#22c55e' } },
        { value: 25, name: 'Beverages', itemStyle: { color: '#facc15' } },
        { value: 20, name: 'Canned Goods', itemStyle: { color: '#3b82f6' } },
        { value: 12, name: 'Personal Care', itemStyle: { color: '#f59e0b' } },
        { value: 8, name: 'Others', itemStyle: { color: '#8b5cf6' } }
      ]

      // Generate trend data (last 30 days)
      const trendData = Array.from({ length: 30 }, (_, i) => {
        const baseValue = 2500
        const trendFactor = i * 50 // Growing trend
        const randomFactor = (Math.random() - 0.5) * 500
        return Math.max(1000, Math.floor(baseValue + trendFactor + randomFactor))
      })

      // Generate hourly data (24 hours)
      const hourlyData = Array.from({ length: 24 }, (_, i) => {
        const baseValue = 1000
        const peakHours = [9, 10, 11, 14, 15, 16, 19, 20] // Business hours
        const peakFactor = peakHours.includes(i) ? 800 : 0
        const randomFactor = (Math.random() - 0.5) * 300
        return Math.max(200, Math.floor(baseValue + peakFactor + randomFactor))
      })

      // Generate weekly data
      const weeklyData = Array.from({ length: 52 }, (_, i) => {
        const baseValue = 25000
        const seasonalFactor = Math.sin((i / 52) * 2 * Math.PI) * 8000
        const randomFactor = (Math.random() - 0.5) * 5000
        return Math.max(12000, Math.floor(baseValue + seasonalFactor + randomFactor))
      })

      setChartData({
        salesData,
        debtData,
        categoryData,
        trendData,
        hourlyData,
        weeklyData
      })

      setLastUpdated(new Date())
      setIsLoading(false)
    }, 800)
  }, [])

  useEffect(() => {
    generateAdvancedData()
  }, [generateAdvancedData])

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      generateAdvancedData()
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh, generateAdvancedData])

  // Advanced chart configurations with theme support
  const getChartTheme = () => ({
    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
    textStyle: {
      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937',
      fontFamily: 'Inter, system-ui, sans-serif'
    },
    grid: {
      borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
    }
  })

  // Enhanced Sales Chart with advanced features
  const salesChartOption = useMemo(() => ({
    ...getChartTheme(),
    title: {
      text: 'Monthly Sales Revenue',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
      },
      formatter: (params: any) => {
        const data = params[0]
        const percentage = chartData.salesData.length > 1
          ? ((data.value - chartData.salesData[Math.max(0, data.dataIndex - 1)]) / chartData.salesData[Math.max(0, data.dataIndex - 1)] * 100).toFixed(1)
          : '0'
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
            <div style="color: #22c55e;">Revenue: ₱${data.value.toLocaleString()}</div>
            <div style="color: ${percentage.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;">
              ${percentage.startsWith('-') ? '' : '+'}${percentage}% from previous month
            </div>
          </div>
        `
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#22c55e'
        }
      }
    },
    legend: {
      data: ['Revenue', 'Target'],
      top: 40,
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      }
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        name: 'Revenue',
        data: chartData.salesData,
        type: filters.chartType,
        smooth: true,
        lineStyle: {
          color: '#22c55e',
          width: 3,
        },
        itemStyle: {
          color: '#22c55e',
          borderRadius: filters.chartType === 'bar' ? [4, 4, 0, 0] : 0
        },
        areaStyle: filters.chartType === 'area' ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(34, 197, 94, 0.4)' },
              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },
            ],
          },
        } : undefined,
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(34, 197, 94, 0.5)'
          }
        },
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ],
          itemStyle: {
            color: '#facc15'
          }
        },
        markLine: {
          data: [
            { type: 'average', name: 'Average' }
          ],
          lineStyle: {
            color: '#f59e0b',
            type: 'dashed'
          }
        }
      },
      {
        name: 'Target',
        data: chartData.salesData.map(val => val * 1.2),
        type: 'line',
        lineStyle: {
          color: '#facc15',
          width: 2,
          type: 'dashed'
        },
        itemStyle: {
          color: '#facc15'
        },
        symbol: 'none'
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z',
        handleSize: '80%',
        handleStyle: {
          color: '#22c55e',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ],
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {
          pixelRatio: 2
        }
      },
      iconStyle: {
        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      }
    }
  }), [chartData.salesData, filters.chartType, resolvedTheme])

  // Enhanced Debt Chart with advanced features
  const debtChartOption = useMemo(() => ({
    ...getChartTheme(),
    title: {
      text: 'Weekly Customer Debt Trends',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
      },
      formatter: (params: any) => {
        const data = params[0]
        const avgDebt = chartData.debtData.reduce((a, b) => a + b, 0) / chartData.debtData.length
        const comparison = ((data.value - avgDebt) / avgDebt * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
            <div style="color: #facc15;">Total Debt: ₱${data.value.toLocaleString()}</div>
            <div style="color: ${comparison.startsWith('-') ? '#22c55e' : '#ef4444'}; font-size: 12px;">
              ${comparison.startsWith('-') ? '' : '+'}${comparison}% vs average
            </div>
          </div>
        `
      },
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        data: chartData.debtData,
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#facc15' },
              { offset: 0.5, color: '#f59e0b' },
              { offset: 1, color: '#eab308' },
            ],
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#fbbf24' },
                { offset: 1, color: '#d97706' },
              ],
            },
            shadowBlur: 10,
            shadowColor: 'rgba(245, 158, 11, 0.5)'
          },
        },
        markLine: {
          data: [
            { type: 'average', name: 'Average' }
          ],
          lineStyle: {
            color: '#ef4444',
            type: 'dashed'
          }
        }
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true,
    },
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }), [chartData.debtData, resolvedTheme])

  // Enhanced Category Chart with interactive features
  const categoryChartOption = useMemo(() => ({
    ...getChartTheme(),
    title: {
      text: 'Product Categories Distribution',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
      },
      formatter: (params: any) => {
        const total = chartData.categoryData.reduce((sum, item) => sum + item.value, 0)
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
            <div style="color: ${params.color};">Products: ${params.value}</div>
            <div style="color: #6b7280; font-size: 12px;">
              ${params.percent}% of total (${total} products)
            </div>
          </div>
        `
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      }
    },
    series: [
      {
        name: 'Categories',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 24,
            fontWeight: 'bold',
            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          scale: true,
          scaleSize: 5
        },
        labelLine: {
          show: false,
        },
        data: chartData.categoryData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => idx * 100
      },
    ],
  }), [chartData.categoryData, resolvedTheme])

  // New Trend Analysis Chart
  const trendChartOption = useMemo(() => ({
    ...getChartTheme(),
    title: {
      text: 'Daily Sales Trend (Last 30 Days)',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
      },
      formatter: (params: any) => {
        const data = params[0]
        const dayIndex = data.dataIndex
        const previousValue = dayIndex > 0 ? chartData.trendData[dayIndex - 1] : data.value
        const change = ((data.value - previousValue) / previousValue * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Day ${dayIndex + 1}</div>
            <div style="color: #3b82f6;">Sales: ₱${data.value.toLocaleString()}</div>
            <div style="color: ${change.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;">
              ${change.startsWith('-') ? '' : '+'}${change}% from previous day
            </div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
        interval: 4
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        data: chartData.trendData,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#3b82f6',
          width: 3,
        },
        itemStyle: {
          color: '#3b82f6',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },
            ],
          },
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.5)'
          }
        }
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100
      }
    ]
  }), [chartData.trendData, resolvedTheme])

  // Enhanced KPI calculations with advanced metrics
  const kpiCards = useMemo(() => {
    const totalRevenue = chartData.salesData.reduce((a, b) => a + b, 0)
    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length
    const totalDebt = chartData.debtData.reduce((a, b) => a + b, 0)
    const avgDailyDebt = totalDebt / chartData.debtData.length

    return [
      {
        title: 'Total Revenue',
        value: '₱' + totalRevenue.toLocaleString(),
        icon: DollarSign,
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        change: '+12.5%',
        changeColor: 'text-green-600 dark:text-green-400',
        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`,
        trend: 'up'
      },
      {
        title: 'Products Listed',
        value: stats.totalProducts.toString(),
        icon: Package,
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        change: '+5.2%',
        changeColor: 'text-blue-600 dark:text-blue-400',
        subtitle: `${stats.lowStockItems} low stock`,
        trend: 'up'
      },
      {
        title: 'Active Customers',
        value: stats.totalDebts.toString(),
        icon: Users,
        color: 'text-purple-600 dark:text-purple-400',
        bgColor: 'bg-purple-50 dark:bg-purple-900/20',
        change: '+8.1%',
        changeColor: 'text-purple-600 dark:text-purple-400',
        subtitle: 'With outstanding debt',
        trend: 'up'
      },
      {
        title: 'Outstanding Debt',
        value: '₱' + stats.totalDebtAmount.toLocaleString(),
        icon: TrendingUp,
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
        change: '-3.2%',
        changeColor: 'text-red-600 dark:text-red-400',
        subtitle: `Avg: ₱${avgDailyDebt.toLocaleString()}/day`,
        trend: 'down'
      },
    ]
  }, [chartData, stats])

  // Filter and export handlers
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleExport = (format: 'pdf' | 'excel' | 'csv' | 'png') => {
    setShowExportMenu(false)
    // Simulate export process
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      // In a real app, this would trigger the actual export
      console.log(`Exporting data as ${format}`)
    }, 2000)
  }

  const refreshData = () => {
    generateAdvancedData()
  }

  const getActiveChartOption = () => {
    switch (activeChart) {
      case 'sales':
        return salesChartOption
      case 'debt':
        return debtChartOption
      case 'category':
        return categoryChartOption
      case 'trend':
        return trendChartOption
      default:
        return salesChartOption
    }
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header with Controls */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              API Graphing & Visuals
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Advanced analytics and business insights dashboard
            </p>
          </div>

          <div className="flex flex-wrap items-center gap-3">
            {/* Auto Refresh Toggle */}
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                autoRefresh
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              <Activity className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />
              Auto Refresh
            </button>

            {/* Manual Refresh */}
            <button
              onClick={refreshData}
              disabled={isLoading}
              className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded-lg text-sm font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all duration-200"
            >
              <Filter className="h-4 w-4" />
              Filters
              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
            </button>

            {/* Export Menu */}
            <div className="relative">
              <button
                onClick={() => setShowExportMenu(!showExportMenu)}
                className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg text-sm font-medium hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200"
              >
                <Download className="h-4 w-4" />
                Export
              </button>

              {showExportMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                  <div className="p-2">
                    <button
                      onClick={() => handleExport('pdf')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileText className="h-4 w-4" />
                      Export as PDF
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileSpreadsheet className="h-4 w-4" />
                      Export as Excel
                    </button>
                    <button
                      onClick={() => handleExport('csv')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileText className="h-4 w-4" />
                      Export as CSV
                    </button>
                    <button
                      onClick={() => handleExport('png')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <Image className="h-4 w-4" />
                      Export as PNG
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 animate-fade-in-up">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date Range
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  <option value="snacks">Snacks</option>
                  <option value="beverages">Beverages</option>
                  <option value="canned">Canned Goods</option>
                  <option value="personal">Personal Care</option>
                  <option value="others">Others</option>
                </select>
              </div>

              {/* Chart Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Chart Type
                </label>
                <select
                  value={filters.chartType}
                  onChange={(e) => handleFilterChange('chartType', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="line">Line Chart</option>
                  <option value="bar">Bar Chart</option>
                  <option value="area">Area Chart</option>
                </select>
              </div>

              {/* Search Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search data..."
                    className="w-full pl-10 pr-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiCards.map((kpi, index) => (
          <div
            key={index}
            className="card p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {kpi.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                  {kpi.value}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {kpi.subtitle}
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <span className={`text-sm font-medium ${kpi.changeColor}`}>
                    {kpi.change}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />
              </div>
            </div>

            {/* Mini trend indicator */}
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  kpi.trend === 'up' ? 'bg-green-500' : 'bg-red-500'
                } animate-pulse`}></div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {kpi.trend === 'up' ? 'Trending up' : 'Trending down'}
                </span>
              </div>
              <Eye className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          </div>
        ))}
      </div>

      {/* Chart Navigation Tabs */}
      <div className="card p-6">
        <div className="flex flex-wrap items-center gap-2 mb-6">
          {[
            { id: 'sales', label: 'Sales Revenue', icon: LineChart },
            { id: 'debt', label: 'Customer Debt', icon: BarChart3 },
            { id: 'category', label: 'Categories', icon: PieChart },
            { id: 'trend', label: 'Daily Trend', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveChart(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeChart === tab.id
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}

          <div className="ml-auto flex items-center gap-2">
            <button
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200"
              title="Fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
            <button
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200"
              title="Chart Settings"
            >
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Main Chart Display */}
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-lg">
              <div className="flex items-center gap-3">
                <RefreshCw className="h-5 w-5 animate-spin text-green-600" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Loading chart data...
                </span>
              </div>
            </div>
          )}

          <div className="transition-opacity duration-300" style={{ opacity: isLoading ? 0.5 : 1 }}>
            <ReactECharts
              option={getActiveChartOption()}
              style={{ height: '500px' }}
              opts={{ renderer: 'svg' }}
            />
          </div>
        </div>
      </div>

      {/* Additional Charts Grid */}
      {activeChart === 'sales' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in">
          {/* Hourly Sales Pattern */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Hourly Sales Pattern
            </h3>
            <ReactECharts
              option={{
                ...getChartTheme(),
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
                  textStyle: {
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }
                },
                xAxis: {
                  type: 'category',
                  data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
                  axisLabel: {
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
                    interval: 3
                  }
                },
                yAxis: {
                  type: 'value',
                  axisLabel: {
                    formatter: '₱{value}',
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }
                },
                series: [{
                  data: chartData.hourlyData,
                  type: 'bar',
                  itemStyle: {
                    color: '#8b5cf6',
                    borderRadius: [2, 2, 0, 0]
                  }
                }],
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '8%',
                  top: '5%',
                  containLabel: true
                }
              }}
              style={{ height: '300px' }}
            />
          </div>

          {/* Weekly Performance */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Weekly Performance
            </h3>
            <ReactECharts
              option={{
                ...getChartTheme(),
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
                  textStyle: {
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }
                },
                xAxis: {
                  type: 'category',
                  data: Array.from({ length: 52 }, (_, i) => `W${i + 1}`),
                  axisLabel: {
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
                    interval: 7
                  }
                },
                yAxis: {
                  type: 'value',
                  axisLabel: {
                    formatter: '₱{value}',
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }
                },
                series: [{
                  data: chartData.weeklyData,
                  type: 'line',
                  smooth: true,
                  lineStyle: {
                    color: '#f59e0b',
                    width: 2
                  },
                  itemStyle: {
                    color: '#f59e0b'
                  },
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
                        { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }
                      ]
                    }
                  }
                }],
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '8%',
                  top: '5%',
                  containLabel: true
                },
                dataZoom: [{
                  type: 'inside',
                  start: 80,
                  end: 100
                }]
              }}
              style={{ height: '300px' }}
            />
          </div>
        </div>
      )}

      {/* Real-time Status Footer */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full animate-pulse ${
              autoRefresh ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {autoRefresh ? 'Real-time Data Updates Active' : 'Real-time Updates Paused'}
            </span>
            {isLoading && (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-sm text-blue-600 dark:text-blue-400">Updating...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4" />
              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
