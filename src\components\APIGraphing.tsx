'use client'

import { useEffect, useState } from 'react'
import ReactECharts from 'echarts-for-react'
import { TrendingUp, DollarSign, Package, Users } from 'lucide-react'

import type { DashboardStats } from '@/types'

interface APIGraphingProps {
  stats: DashboardStats
}

export default function APIGraphing({ stats }: APIGraphingProps) {
  const [salesData, setSalesData] = useState<number[]>([])
  const [debtData, setDebtData] = useState<number[]>([])

  useEffect(() => {
    // Generate sample data for charts
    setSalesData([25000, 32000, 28000, 45000, 38000, 52000, 48000, 55000, 42000, 38000, 46000, 58000])
    setDebtData([8000, 12000, 9500, 15000, 11000, 7500, 6000])
  }, [])

  const salesChartOption = {
    title: {
      text: 'Monthly Sales Revenue',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => `${params[0].name}<br/>Revenue: ₱${params[0].value.toLocaleString()}`,
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '₱{value}' },
    },
    series: [{
      data: salesData,
      type: 'line',
      smooth: true,
      lineStyle: { color: '#22c55e', width: 3 },
      itemStyle: { color: '#22c55e' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(34, 197, 94, 0.3)' },
            { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },
          ],
        },
      },
    }],
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  }

  const debtChartOption = {
    title: {
      text: 'Weekly Debt Analysis',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => `${params[0].name}<br/>Debt: ₱${params[0].value.toLocaleString()}`,
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '₱{value}' },
    },
    series: [{
      data: debtData,
      type: 'bar',
      itemStyle: { color: '#f59e0b', borderRadius: [4, 4, 0, 0] },
    }],
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            API Graphing & Visuals
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Advanced analytics and data visualization
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">₱{stats.totalProducts * 1500}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Products</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalProducts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
              <Users className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Debts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">₱{stats.totalDebtAmount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <TrendingUp className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Stock</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.lowStockItems}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <ReactECharts option={salesChartOption} style={{ height: '400px' }} />
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <ReactECharts option={debtChartOption} style={{ height: '400px' }} />
        </div>
      </div>
    </div>
  )
}
