'use client'

import { useEffect, useState } from 'react'
import ReactECharts from 'echarts-for-react'
import { TrendingUp, DollarSign, Package, Users } from 'lucide-react'

import type { DashboardStats } from '@/types'

interface APIGraphingProps {
  stats: DashboardStats
}

export default function APIGraphing({ stats }: APIGraphingProps) {
  const [salesData, setSalesData] = useState<number[]>([])
  const [debtData, setDebtData] = useState<number[]>([])

  useEffect(() => {
    // Generate sample data for charts
    setSalesData([25000, 32000, 28000, 45000, 38000, 52000, 48000, 55000, 42000, 38000, 46000, 58000])
    setDebtData([8000, 12000, 9500, 15000, 11000, 7500, 6000])
  }, [])

  const salesChartOption = {
    title: {
      text: 'Monthly Sales Revenue',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => `${params[0].name}<br/>Revenue: ₱${params[0].value.toLocaleString()}`,
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '₱{value}' },
    },
    series: [{
      data: salesData,
      type: 'line',
      smooth: true,
      lineStyle: { color: '#22c55e', width: 3 },
      itemStyle: { color: '#22c55e' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(34, 197, 94, 0.3)' },
            { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },
          ],
        },
      },
    }],
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  }

  const debtChartOption = {
    title: {
      text: 'Weekly Debt Analysis',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => `${params[0].name}<br/>Debt: ₱${params[0].value.toLocaleString()}`,
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '₱{value}' },
    },
    series: [{
      data: debtData,
      type: 'bar',
      itemStyle: { color: '#f59e0b', borderRadius: [4, 4, 0, 0] },
    }],
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            API Graphing & Visuals
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Advanced analytics and data visualization
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">₱{stats.totalProducts * 1500}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Products</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalProducts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
              <Users className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Debts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">₱{stats.totalDebtAmount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <TrendingUp className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Stock</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.lowStockItems}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <ReactECharts option={salesChartOption} style={{ height: '400px' }} />
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <ReactECharts option={debtChartOption} style={{ height: '400px' }} />
        </div>
      </div>
    </div>
  )
}
    title: {
      text: 'Daily Sales Trend (Last 30 Days)',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
      textStyle: {
        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
      },
      formatter: (params: any) => {
        const data = params[0]
        const dayIndex = data.dataIndex
        const previousValue = dayIndex > 0 ? chartData.trendData[dayIndex - 1] : data.value
        const change = ((data.value - previousValue) / previousValue * 100).toFixed(1)
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Day ${dayIndex + 1}</div>
            <div style="color: #3b82f6;">Sales: ₱${data.value.toLocaleString()}</div>
            <div style="color: ${change.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;">
              ${change.startsWith('-') ? '' : '+'}${change}% from previous day
            </div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      axisLabel: {
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
        interval: 4
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'
        }
      },
      splitLine: {
        lineStyle: {
          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
        }
      }
    },
    series: [
      {
        data: chartData.trendData,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#3b82f6',
          width: 3,
        },
        itemStyle: {
          color: '#3b82f6',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },
            ],
          },
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.5)'
          }
        }
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100
      }
    ]
  }), [chartData.trendData, resolvedTheme])

  // Enhanced KPI calculations with advanced metrics
  const kpiCards = useMemo(() => {
    const totalRevenue = chartData.salesData.reduce((a, b) => a + b, 0)
    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length
    const totalDebt = chartData.debtData.reduce((a, b) => a + b, 0)
    const avgDailyDebt = totalDebt / chartData.debtData.length

    return [
      {
        title: 'Total Revenue',
        value: '₱' + totalRevenue.toLocaleString(),
        icon: DollarSign,
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        change: '+12.5%',
        changeColor: 'text-green-600 dark:text-green-400',
        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`,
        trend: 'up'
      },
      {
        title: 'Products Listed',
        value: stats.totalProducts.toString(),
        icon: Package,
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        change: '+5.2%',
        changeColor: 'text-blue-600 dark:text-blue-400',
        subtitle: `${stats.lowStockItems} low stock`,
        trend: 'up'
      },
      {
        title: 'Active Customers',
        value: stats.totalDebts.toString(),
        icon: Users,
        color: 'text-purple-600 dark:text-purple-400',
        bgColor: 'bg-purple-50 dark:bg-purple-900/20',
        change: '+8.1%',
        changeColor: 'text-purple-600 dark:text-purple-400',
        subtitle: 'With outstanding debt',
        trend: 'up'
      },
      {
        title: 'Outstanding Debt',
        value: '₱' + stats.totalDebtAmount.toLocaleString(),
        icon: TrendingUp,
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
        change: '-3.2%',
        changeColor: 'text-red-600 dark:text-red-400',
        subtitle: `Avg: ₱${avgDailyDebt.toLocaleString()}/day`,
        trend: 'down'
      },
    ]
  }, [chartData, stats])

  // Filter and export handlers
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleExport = (format: 'pdf' | 'excel' | 'csv' | 'png') => {
    setShowExportMenu(false)
    // Simulate export process
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      // In a real app, this would trigger the actual export
      console.log(`Exporting data as ${format}`)
    }, 2000)
  }

  const refreshData = () => {
    generateAdvancedData()
  }

  const getActiveChartOption = () => {
    switch (activeChart) {
      case 'sales':
        return salesChartOption
      case 'debt':
        return debtChartOption
      case 'category':
        return categoryChartOption
      case 'trend':
        return trendChartOption
      default:
        return salesChartOption
    }
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header with Controls */}
      <div className="card p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              API Graphing & Visuals
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Advanced analytics and business insights dashboard
            </p>
          </div>

          <div className="flex flex-wrap items-center gap-3">
            {/* Auto Refresh Toggle */}
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                autoRefresh
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              <Activity className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />
              Auto Refresh
            </button>

            {/* Manual Refresh */}
            <button
              onClick={refreshData}
              disabled={isLoading}
              className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded-lg text-sm font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all duration-200"
            >
              <Filter className="h-4 w-4" />
              Filters
              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
            </button>

            {/* Export Menu */}
            <div className="relative">
              <button
                onClick={() => setShowExportMenu(!showExportMenu)}
                className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg text-sm font-medium hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200"
              >
                <Download className="h-4 w-4" />
                Export
              </button>

              {showExportMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                  <div className="p-2">
                    <button
                      onClick={() => handleExport('pdf')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileText className="h-4 w-4" />
                      Export as PDF
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileSpreadsheet className="h-4 w-4" />
                      Export as Excel
                    </button>
                    <button
                      onClick={() => handleExport('csv')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <FileText className="h-4 w-4" />
                      Export as CSV
                    </button>
                    <button
                      onClick={() => handleExport('png')}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                    >
                      <Image className="h-4 w-4" />
                      Export as PNG
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 animate-fade-in-up">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date Range
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  <option value="snacks">Snacks</option>
                  <option value="beverages">Beverages</option>
                  <option value="canned">Canned Goods</option>
                  <option value="personal">Personal Care</option>
                  <option value="others">Others</option>
                </select>
              </div>

              {/* Chart Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Chart Type
                </label>
                <select
                  value={filters.chartType}
                  onChange={(e) => handleFilterChange('chartType', e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="line">Line Chart</option>
                  <option value="bar">Bar Chart</option>
                  <option value="area">Area Chart</option>
                </select>
              </div>

              {/* Search Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search data..."
                    className="w-full pl-10 pr-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiCards.map((kpi, index) => (
          <div
            key={index}
            className="card p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {kpi.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                  {kpi.value}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {kpi.subtitle}
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <span className={`text-sm font-medium ${kpi.changeColor}`}>
                    {kpi.change}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />
              </div>
            </div>

            {/* Mini trend indicator */}
            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  kpi.trend === 'up' ? 'bg-green-500' : 'bg-red-500'
                } animate-pulse`}></div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {kpi.trend === 'up' ? 'Trending up' : 'Trending down'}
                </span>
              </div>
              <Eye className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          </div>
        ))}
      </div>

      {/* Chart Navigation Tabs */}
      <div className="card p-6">
        <div className="flex flex-wrap items-center gap-2 mb-6">
          {[
            { id: 'sales', label: 'Sales Revenue', icon: LineChart },
            { id: 'debt', label: 'Customer Debt', icon: BarChart3 },
            { id: 'category', label: 'Categories', icon: PieChart },
            { id: 'trend', label: 'Daily Trend', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveChart(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeChart === tab.id
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}

          <div className="ml-auto flex items-center gap-2">
            <button
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200"
              title="Fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
            <button
              className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200"
              title="Chart Settings"
            >
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Main Chart Display */}
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-lg">
              <div className="flex items-center gap-3">
                <RefreshCw className="h-5 w-5 animate-spin text-green-600" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Loading chart data...
                </span>
              </div>
            </div>
          )}

          <div className="transition-opacity duration-300" style={{ opacity: isLoading ? 0.5 : 1 }}>
            <ReactECharts
              option={getActiveChartOption()}
              style={{ height: '500px' }}
              opts={{ renderer: 'svg' }}
            />
          </div>
        </div>
      </div>

      {/* Additional Charts Grid */}
      {activeChart === 'sales' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in">
          {/* Hourly Sales Pattern */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Hourly Sales Pattern
            </h3>
            <ReactECharts
              option={{
                ...getChartTheme(),
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
                  textStyle: {
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }
                },
                xAxis: {
                  type: 'category',
                  data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
                  axisLabel: {
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
                    interval: 3
                  }
                },
                yAxis: {
                  type: 'value',
                  axisLabel: {
                    formatter: '₱{value}',
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }
                },
                series: [{
                  data: chartData.hourlyData,
                  type: 'bar',
                  itemStyle: {
                    color: '#8b5cf6',
                    borderRadius: [2, 2, 0, 0]
                  }
                }],
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '8%',
                  top: '5%',
                  containLabel: true
                }
              }}
              style={{ height: '300px' }}
            />
          </div>

          {/* Weekly Performance */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Weekly Performance
            </h3>
            <ReactECharts
              option={{
                ...getChartTheme(),
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',
                  textStyle: {
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }
                },
                xAxis: {
                  type: 'category',
                  data: Array.from({ length: 52 }, (_, i) => `W${i + 1}`),
                  axisLabel: {
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',
                    interval: 7
                  }
                },
                yAxis: {
                  type: 'value',
                  axisLabel: {
                    formatter: '₱{value}',
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }
                },
                series: [{
                  data: chartData.weeklyData,
                  type: 'line',
                  smooth: true,
                  lineStyle: {
                    color: '#f59e0b',
                    width: 2
                  },
                  itemStyle: {
                    color: '#f59e0b'
                  },
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
                        { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }
                      ]
                    }
                  }
                }],
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '8%',
                  top: '5%',
                  containLabel: true
                },
                dataZoom: [{
                  type: 'inside',
                  start: 80,
                  end: 100
                }]
              }}
              style={{ height: '300px' }}
            />
          </div>
        </div>
      )}

      {/* Real-time Status Footer */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full animate-pulse ${
              autoRefresh ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {autoRefresh ? 'Real-time Data Updates Active' : 'Real-time Updates Paused'}
            </span>
            {isLoading && (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-sm text-blue-600 dark:text-blue-400">Updating...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4" />
              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
