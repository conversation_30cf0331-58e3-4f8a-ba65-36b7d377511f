{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/APIGraphing.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport ReactECharts from 'echarts-for-react'\nimport { TrendingUp, DollarSign, Package, Users, Calendar } from 'lucide-react'\n\nimport type { DashboardStats } from '@/types'\n\ninterface APIGraphingProps {\n  stats: DashboardStats\n}\n\nexport default function APIGraphing({ stats }: APIGraphingProps) {\n  const [salesData, setSalesData] = useState<number[]>([])\n  const [debtData, setDebtData] = useState<number[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n\n  useEffect(() => {\n    // Simulate API data for charts\n    const generateSalesData = () => {\n      const data = []\n      for (let i = 0; i < 12; i++) {\n        data.push(Math.floor(Math.random() * 50000) + 20000)\n      }\n      setSalesData(data)\n    }\n\n    const generateDebtData = () => {\n      const data = []\n      for (let i = 0; i < 7; i++) {\n        data.push(Math.floor(Math.random() * 15000) + 5000)\n      }\n      setDebtData(data)\n    }\n\n    generateSalesData()\n    generateDebtData()\n  }, [])\n\n  // Advanced chart configurations with theme support\n  const getChartTheme = () => ({\n    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n    textStyle: {\n      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937',\n      fontFamily: 'Inter, system-ui, sans-serif'\n    },\n    grid: {\n      borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n    }\n  })\n\n  // Enhanced Sales Chart with advanced features\n  const salesChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Monthly Sales Revenue',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const percentage = chartData.salesData.length > 1\n          ? ((data.value - chartData.salesData[Math.max(0, data.dataIndex - 1)]) / chartData.salesData[Math.max(0, data.dataIndex - 1)] * 100).toFixed(1)\n          : '0'\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"color: #22c55e;\">Revenue: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${percentage.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;\">\n              ${percentage.startsWith('-') ? '' : '+'}${percentage}% from previous month\n            </div>\n          </div>\n        `\n      },\n      axisPointer: {\n        type: 'cross',\n        crossStyle: {\n          color: '#22c55e'\n        }\n      }\n    },\n    legend: {\n      data: ['Revenue', 'Target'],\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Revenue',\n        data: chartData.salesData,\n        type: filters.chartType,\n        smooth: true,\n        lineStyle: {\n          color: '#22c55e',\n          width: 3,\n        },\n        itemStyle: {\n          color: '#22c55e',\n          borderRadius: filters.chartType === 'bar' ? [4, 4, 0, 0] : 0\n        },\n        areaStyle: filters.chartType === 'area' ? {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(34, 197, 94, 0.4)' },\n              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },\n            ],\n          },\n        } : undefined,\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(34, 197, 94, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Max' },\n            { type: 'min', name: 'Min' }\n          ],\n          itemStyle: {\n            color: '#facc15'\n          }\n        },\n        markLine: {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#f59e0b',\n            type: 'dashed'\n          }\n        }\n      },\n      {\n        name: 'Target',\n        data: chartData.salesData.map(val => val * 1.2),\n        type: 'line',\n        lineStyle: {\n          color: '#facc15',\n          width: 2,\n          type: 'dashed'\n        },\n        itemStyle: {\n          color: '#facc15'\n        },\n        symbol: 'none'\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '8%',\n      top: '15%',\n      containLabel: true,\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 0,\n        end: 100\n      },\n      {\n        start: 0,\n        end: 100,\n        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z',\n        handleSize: '80%',\n        handleStyle: {\n          color: '#22c55e',\n          shadowBlur: 3,\n          shadowColor: 'rgba(0, 0, 0, 0.6)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      }\n    ],\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.salesData, filters.chartType, resolvedTheme])\n\n  // Enhanced Debt Chart with advanced features\n  const debtChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Weekly Customer Debt Trends',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const avgDebt = chartData.debtData.reduce((a, b) => a + b, 0) / chartData.debtData.length\n        const comparison = ((data.value - avgDebt) / avgDebt * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"color: #facc15;\">Total Debt: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${comparison.startsWith('-') ? '#22c55e' : '#ef4444'}; font-size: 12px;\">\n              ${comparison.startsWith('-') ? '' : '+'}${comparison}% vs average\n            </div>\n          </div>\n        `\n      },\n      axisPointer: {\n        type: 'shadow'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n        rotate: 45\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        data: chartData.debtData,\n        type: 'bar',\n        barWidth: '60%',\n        itemStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: '#facc15' },\n              { offset: 0.5, color: '#f59e0b' },\n              { offset: 1, color: '#eab308' },\n            ],\n          },\n          borderRadius: [4, 4, 0, 0]\n        },\n        emphasis: {\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [\n                { offset: 0, color: '#fbbf24' },\n                { offset: 1, color: '#d97706' },\n              ],\n            },\n            shadowBlur: 10,\n            shadowColor: 'rgba(245, 158, 11, 0.5)'\n          },\n        },\n        markLine: {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#ef4444',\n            type: 'dashed'\n          }\n        }\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '15%',\n      top: '15%',\n      containLabel: true,\n    },\n    animation: true,\n    animationDuration: 1000,\n    animationEasing: 'cubicOut'\n  }), [chartData.debtData, resolvedTheme])\n\n  // Enhanced Category Chart with interactive features\n  const categoryChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Product Categories Distribution',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'item',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const total = chartData.categoryData.reduce((sum, item) => sum + item.value, 0)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${params.name}</div>\n            <div style=\"color: ${params.color};\">Products: ${params.value}</div>\n            <div style=\"color: #6b7280; font-size: 12px;\">\n              ${params.percent}% of total (${total} products)\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      orient: 'horizontal',\n      bottom: 10,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      }\n    },\n    series: [\n      {\n        name: 'Categories',\n        type: 'pie',\n        radius: ['45%', '75%'],\n        center: ['50%', '45%'],\n        avoidLabelOverlap: false,\n        itemStyle: {\n          borderRadius: 8,\n          borderColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          borderWidth: 3,\n          shadowBlur: 10,\n          shadowColor: 'rgba(0, 0, 0, 0.1)'\n        },\n        label: {\n          show: false,\n          position: 'center',\n        },\n        emphasis: {\n          label: {\n            show: true,\n            fontSize: 24,\n            fontWeight: 'bold',\n            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n          },\n          itemStyle: {\n            shadowBlur: 20,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.3)'\n          },\n          scale: true,\n          scaleSize: 5\n        },\n        labelLine: {\n          show: false,\n        },\n        data: chartData.categoryData,\n        animationType: 'scale',\n        animationEasing: 'elasticOut',\n        animationDelay: (idx: number) => idx * 100\n      },\n    ],\n  }), [chartData.categoryData, resolvedTheme])\n\n  // New Trend Analysis Chart\n  const trendChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Daily Sales Trend (Last 30 Days)',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const dayIndex = data.dataIndex\n        const previousValue = dayIndex > 0 ? chartData.trendData[dayIndex - 1] : data.value\n        const change = ((data.value - previousValue) / previousValue * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">Day ${dayIndex + 1}</div>\n            <div style=\"color: #3b82f6;\">Sales: ₱${data.value.toLocaleString()}</div>\n            <div style=\"color: ${change.startsWith('-') ? '#ef4444' : '#22c55e'}; font-size: 12px;\">\n              ${change.startsWith('-') ? '' : '+'}${change}% from previous day\n            </div>\n          </div>\n        `\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n        interval: 4\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n        }\n      }\n    },\n    series: [\n      {\n        data: chartData.trendData,\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          color: '#3b82f6',\n          width: 3,\n        },\n        itemStyle: {\n          color: '#3b82f6',\n        },\n        areaStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },\n              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },\n            ],\n          },\n        },\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(59, 130, 246, 0.5)'\n          }\n        }\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '8%',\n      top: '15%',\n      containLabel: true,\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 70,\n        end: 100\n      }\n    ]\n  }), [chartData.trendData, resolvedTheme])\n\n  // Enhanced KPI calculations with advanced metrics\n  const kpiCards = useMemo(() => {\n    const totalRevenue = chartData.salesData.reduce((a, b) => a + b, 0)\n    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length\n    const totalDebt = chartData.debtData.reduce((a, b) => a + b, 0)\n    const avgDailyDebt = totalDebt / chartData.debtData.length\n\n    return [\n      {\n        title: 'Total Revenue',\n        value: '₱' + totalRevenue.toLocaleString(),\n        icon: DollarSign,\n        color: 'text-green-600 dark:text-green-400',\n        bgColor: 'bg-green-50 dark:bg-green-900/20',\n        change: '+12.5%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`,\n        trend: 'up'\n      },\n      {\n        title: 'Products Listed',\n        value: stats.totalProducts.toString(),\n        icon: Package,\n        color: 'text-blue-600 dark:text-blue-400',\n        bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n        change: '+5.2%',\n        changeColor: 'text-blue-600 dark:text-blue-400',\n        subtitle: `${stats.lowStockItems} low stock`,\n        trend: 'up'\n      },\n      {\n        title: 'Active Customers',\n        value: stats.totalDebts.toString(),\n        icon: Users,\n        color: 'text-purple-600 dark:text-purple-400',\n        bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n        change: '+8.1%',\n        changeColor: 'text-purple-600 dark:text-purple-400',\n        subtitle: 'With outstanding debt',\n        trend: 'up'\n      },\n      {\n        title: 'Outstanding Debt',\n        value: '₱' + stats.totalDebtAmount.toLocaleString(),\n        icon: TrendingUp,\n        color: 'text-yellow-600 dark:text-yellow-400',\n        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n        change: '-3.2%',\n        changeColor: 'text-red-600 dark:text-red-400',\n        subtitle: `Avg: ₱${avgDailyDebt.toLocaleString()}/day`,\n        trend: 'down'\n      },\n    ]\n  }, [chartData, stats])\n\n  // Filter and export handlers\n  const handleFilterChange = (key: keyof FilterState, value: any) => {\n    setFilters(prev => ({ ...prev, [key]: value }))\n  }\n\n  const handleExport = (format: 'pdf' | 'excel' | 'csv' | 'png') => {\n    setShowExportMenu(false)\n    // Simulate export process\n    setIsLoading(true)\n    setTimeout(() => {\n      setIsLoading(false)\n      // In a real app, this would trigger the actual export\n      console.log(`Exporting data as ${format}`)\n    }, 2000)\n  }\n\n  const refreshData = () => {\n    generateAdvancedData()\n  }\n\n  const getActiveChartOption = () => {\n    switch (activeChart) {\n      case 'sales':\n        return salesChartOption\n      case 'debt':\n        return debtChartOption\n      case 'category':\n        return categoryChartOption\n      case 'trend':\n        return trendChartOption\n      default:\n        return salesChartOption\n    }\n  }\n\n  return (\n    <div className=\"space-y-6 animate-fade-in\">\n      {/* Header with Controls */}\n      <div className=\"card p-6\">\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              API Graphing & Visuals\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n              Advanced analytics and business insights dashboard\n            </p>\n          </div>\n\n          <div className=\"flex flex-wrap items-center gap-3\">\n            {/* Auto Refresh Toggle */}\n            <button\n              onClick={() => setAutoRefresh(!autoRefresh)}\n              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                autoRefresh\n                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'\n                  : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'\n              }`}\n            >\n              <Activity className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />\n              Auto Refresh\n            </button>\n\n            {/* Manual Refresh */}\n            <button\n              onClick={refreshData}\n              disabled={isLoading}\n              className=\"flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50\"\n            >\n              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n              Refresh\n            </button>\n\n            {/* Filters Toggle */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded-lg text-sm font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all duration-200\"\n            >\n              <Filter className=\"h-4 w-4\" />\n              Filters\n              <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />\n            </button>\n\n            {/* Export Menu */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowExportMenu(!showExportMenu)}\n                className=\"flex items-center gap-2 px-3 py-2 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg text-sm font-medium hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200\"\n              >\n                <Download className=\"h-4 w-4\" />\n                Export\n              </button>\n\n              {showExportMenu && (\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10\">\n                  <div className=\"p-2\">\n                    <button\n                      onClick={() => handleExport('pdf')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileText className=\"h-4 w-4\" />\n                      Export as PDF\n                    </button>\n                    <button\n                      onClick={() => handleExport('excel')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileSpreadsheet className=\"h-4 w-4\" />\n                      Export as Excel\n                    </button>\n                    <button\n                      onClick={() => handleExport('csv')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <FileText className=\"h-4 w-4\" />\n                      Export as CSV\n                    </button>\n                    <button\n                      onClick={() => handleExport('png')}\n                      className=\"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md\"\n                    >\n                      <Image className=\"h-4 w-4\" />\n                      Export as PNG\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Filters Panel */}\n        {showFilters && (\n          <div className=\"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 animate-fade-in-up\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Date Range Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Date Range\n                </label>\n                <select\n                  value={filters.dateRange}\n                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"today\">Today</option>\n                  <option value=\"week\">This Week</option>\n                  <option value=\"month\">This Month</option>\n                  <option value=\"quarter\">This Quarter</option>\n                  <option value=\"year\">This Year</option>\n                  <option value=\"custom\">Custom Range</option>\n                </select>\n              </div>\n\n              {/* Category Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Category\n                </label>\n                <select\n                  value={filters.category}\n                  onChange={(e) => handleFilterChange('category', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Categories</option>\n                  <option value=\"snacks\">Snacks</option>\n                  <option value=\"beverages\">Beverages</option>\n                  <option value=\"canned\">Canned Goods</option>\n                  <option value=\"personal\">Personal Care</option>\n                  <option value=\"others\">Others</option>\n                </select>\n              </div>\n\n              {/* Chart Type Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Chart Type\n                </label>\n                <select\n                  value={filters.chartType}\n                  onChange={(e) => handleFilterChange('chartType', e.target.value)}\n                  className=\"w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                >\n                  <option value=\"line\">Line Chart</option>\n                  <option value=\"bar\">Bar Chart</option>\n                  <option value=\"area\">Area Chart</option>\n                </select>\n              </div>\n\n              {/* Search Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Search\n                </label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search data...\"\n                    className=\"w-full pl-10 pr-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced KPI Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {kpiCards.map((kpi, index) => (\n          <div\n            key={index}\n            className=\"card p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                  {kpi.title}\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white mt-1\">\n                  {kpi.value}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                  {kpi.subtitle}\n                </p>\n                <div className=\"flex items-center gap-1 mt-2\">\n                  <span className={`text-sm font-medium ${kpi.changeColor}`}>\n                    {kpi.change}\n                  </span>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    from last month\n                  </span>\n                </div>\n              </div>\n              <div className={`p-3 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-300`}>\n                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />\n              </div>\n            </div>\n\n            {/* Mini trend indicator */}\n            <div className=\"mt-4 flex items-center justify-between\">\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  kpi.trend === 'up' ? 'bg-green-500' : 'bg-red-500'\n                } animate-pulse`}></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {kpi.trend === 'up' ? 'Trending up' : 'Trending down'}\n                </span>\n              </div>\n              <Eye className=\"h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Chart Navigation Tabs */}\n      <div className=\"card p-6\">\n        <div className=\"flex flex-wrap items-center gap-2 mb-6\">\n          {[\n            { id: 'sales', label: 'Sales Revenue', icon: LineChart },\n            { id: 'debt', label: 'Customer Debt', icon: BarChart3 },\n            { id: 'category', label: 'Categories', icon: PieChart },\n            { id: 'trend', label: 'Daily Trend', icon: TrendingUp }\n          ].map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveChart(tab.id as any)}\n              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                activeChart === tab.id\n                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-md'\n                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'\n              }`}\n            >\n              <tab.icon className=\"h-4 w-4\" />\n              {tab.label}\n            </button>\n          ))}\n\n          <div className=\"ml-auto flex items-center gap-2\">\n            <button\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\"\n              title=\"Fullscreen\"\n            >\n              <Maximize2 className=\"h-4 w-4\" />\n            </button>\n            <button\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\"\n              title=\"Chart Settings\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Main Chart Display */}\n        <div className=\"relative\">\n          {isLoading && (\n            <div className=\"absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center z-10 rounded-lg\">\n              <div className=\"flex items-center gap-3\">\n                <RefreshCw className=\"h-5 w-5 animate-spin text-green-600\" />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Loading chart data...\n                </span>\n              </div>\n            </div>\n          )}\n\n          <div className=\"transition-opacity duration-300\" style={{ opacity: isLoading ? 0.5 : 1 }}>\n            <ReactECharts\n              option={getActiveChartOption()}\n              style={{ height: '500px' }}\n              opts={{ renderer: 'svg' }}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Additional Charts Grid */}\n      {activeChart === 'sales' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in\">\n          {/* Hourly Sales Pattern */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Hourly Sales Pattern\n            </h3>\n            <ReactECharts\n              option={{\n                ...getChartTheme(),\n                tooltip: {\n                  trigger: 'axis',\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n                  textStyle: {\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }\n                },\n                xAxis: {\n                  type: 'category',\n                  data: Array.from({ length: 24 }, (_, i) => `${i}:00`),\n                  axisLabel: {\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n                    interval: 3\n                  }\n                },\n                yAxis: {\n                  type: 'value',\n                  axisLabel: {\n                    formatter: '₱{value}',\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }\n                },\n                series: [{\n                  data: chartData.hourlyData,\n                  type: 'bar',\n                  itemStyle: {\n                    color: '#8b5cf6',\n                    borderRadius: [2, 2, 0, 0]\n                  }\n                }],\n                grid: {\n                  left: '3%',\n                  right: '4%',\n                  bottom: '8%',\n                  top: '5%',\n                  containLabel: true\n                }\n              }}\n              style={{ height: '300px' }}\n            />\n          </div>\n\n          {/* Weekly Performance */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Weekly Performance\n            </h3>\n            <ReactECharts\n              option={{\n                ...getChartTheme(),\n                tooltip: {\n                  trigger: 'axis',\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n                  textStyle: {\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }\n                },\n                xAxis: {\n                  type: 'category',\n                  data: Array.from({ length: 52 }, (_, i) => `W${i + 1}`),\n                  axisLabel: {\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280',\n                    interval: 7\n                  }\n                },\n                yAxis: {\n                  type: 'value',\n                  axisLabel: {\n                    formatter: '₱{value}',\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }\n                },\n                series: [{\n                  data: chartData.weeklyData,\n                  type: 'line',\n                  smooth: true,\n                  lineStyle: {\n                    color: '#f59e0b',\n                    width: 2\n                  },\n                  itemStyle: {\n                    color: '#f59e0b'\n                  },\n                  areaStyle: {\n                    color: {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 0,\n                      y2: 1,\n                      colorStops: [\n                        { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },\n                        { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }\n                      ]\n                    }\n                  }\n                }],\n                grid: {\n                  left: '3%',\n                  right: '4%',\n                  bottom: '8%',\n                  top: '5%',\n                  containLabel: true\n                },\n                dataZoom: [{\n                  type: 'inside',\n                  start: 80,\n                  end: 100\n                }]\n              }}\n              style={{ height: '300px' }}\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Real-time Status Footer */}\n      <div className=\"card p-4\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className={`w-3 h-3 rounded-full animate-pulse ${\n              autoRefresh ? 'bg-green-500' : 'bg-gray-400'\n            }`}></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {autoRefresh ? 'Real-time Data Updates Active' : 'Real-time Updates Paused'}\n            </span>\n            {isLoading && (\n              <div className=\"flex items-center gap-2\">\n                <RefreshCw className=\"h-4 w-4 animate-spin text-blue-600\" />\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">Updating...</span>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-2\">\n              <Calendar className=\"h-4 w-4\" />\n              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Activity className=\"h-4 w-4\" />\n              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAYe,SAAS,YAAY,EAAE,KAAK,EAAoB;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,oBAAoB;YACxB,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;YAChD;YACA,aAAa;QACf;QAEA,MAAM,mBAAmB;YACvB,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;YAChD;YACA,YAAY;QACd;QAEA;QACA;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,gBAAgB,IAAM,CAAC;YAC3B,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,WAAW;gBACT,OAAO,kBAAkB,SAAS,YAAY;gBAC9C,YAAY;YACd;YACA,MAAM;gBACJ,aAAa,kBAAkB,SAAS,YAAY;YACtD;QACF,CAAC;IAED,8CAA8C;IAC9C,MAAM,mBAAmB,QAAQ,IAAM,CAAC;YACtC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,OAAO,MAAM,CAAC,EAAE;oBACtB,MAAM,aAAa,UAAU,SAAS,CAAC,MAAM,GAAG,IAC5C,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,SAAS,GAAG,GAAG,IAAI,UAAU,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,KAC3I;oBACJ,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;mDACzB,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BAClD,EAAE,WAAW,UAAU,CAAC,OAAO,YAAY,UAAU;cACtE,EAAE,WAAW,UAAU,CAAC,OAAO,KAAK,MAAM,WAAW;;;QAG3D,CAAC;gBACH;gBACA,aAAa;oBACX,MAAM;oBACN,YAAY;wBACV,OAAO;oBACT;gBACF;YACF;YACA,QAAQ;gBACN,MAAM;oBAAC;oBAAW;iBAAS;gBAC3B,KAAK;gBACL,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;iBAAM;gBAC1F,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,WAAW;oBACX,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;YACF;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM,UAAU,SAAS;oBACzB,MAAM,QAAQ,SAAS;oBACvB,QAAQ;oBACR,WAAW;wBACT,OAAO;wBACP,OAAO;oBACT;oBACA,WAAW;wBACT,OAAO;wBACP,cAAc,QAAQ,SAAS,KAAK,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,GAAG;oBAC7D;oBACA,WAAW,QAAQ,SAAS,KAAK,SAAS;wBACxC,OAAO;4BACL,MAAM;4BACN,GAAG;4BACH,GAAG;4BACH,IAAI;4BACJ,IAAI;4BACJ,YAAY;gCACV;oCAAE,QAAQ;oCAAG,OAAO;gCAAyB;gCAC7C;oCAAE,QAAQ;oCAAG,OAAO;gCAA0B;6BAC/C;wBACH;oBACF,IAAI;oBACJ,UAAU;wBACR,OAAO;wBACP,WAAW;4BACT,YAAY;4BACZ,aAAa;wBACf;oBACF;oBACA,WAAW;wBACT,MAAM;4BACJ;gCAAE,MAAM;gCAAO,MAAM;4BAAM;4BAC3B;gCAAE,MAAM;gCAAO,MAAM;4BAAM;yBAC5B;wBACD,WAAW;4BACT,OAAO;wBACT;oBACF;oBACA,UAAU;wBACR,MAAM;4BACJ;gCAAE,MAAM;gCAAW,MAAM;4BAAU;yBACpC;wBACD,WAAW;4BACT,OAAO;4BACP,MAAM;wBACR;oBACF;gBACF;gBACA;oBACE,MAAM;oBACN,MAAM,UAAU,SAAS,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM;oBAC3C,MAAM;oBACN,WAAW;wBACT,OAAO;wBACP,OAAO;wBACP,MAAM;oBACR;oBACA,WAAW;wBACT,OAAO;oBACT;oBACA,QAAQ;gBACV;aACD;YACD,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,cAAc;YAChB;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,YAAY;oBACZ,YAAY;oBACZ,aAAa;wBACX,OAAO;wBACP,YAAY;wBACZ,aAAa;wBACb,eAAe;wBACf,eAAe;oBACjB;gBACF;aACD;YACD,SAAS;gBACP,SAAS;oBACP,UAAU;wBACR,YAAY;oBACd;oBACA,SAAS,CAAC;oBACV,aAAa;wBACX,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,aAAa,kBAAkB,SAAS,YAAY;gBACtD;YACF;QACF,CAAC,GAAG;QAAC,UAAU,SAAS;QAAE,QAAQ,SAAS;QAAE;KAAc;IAE3D,6CAA6C;IAC7C,MAAM,kBAAkB,QAAQ,IAAM,CAAC;YACrC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,OAAO,MAAM,CAAC,EAAE;oBACtB,MAAM,UAAU,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,UAAU,QAAQ,CAAC,MAAM;oBACzF,MAAM,aAAa,CAAC,CAAC,KAAK,KAAK,GAAG,OAAO,IAAI,UAAU,GAAG,EAAE,OAAO,CAAC;oBACpE,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;sDACtB,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BACrD,EAAE,WAAW,UAAU,CAAC,OAAO,YAAY,UAAU;cACtE,EAAE,WAAW,UAAU,CAAC,OAAO,KAAK,MAAM,WAAW;;;QAG3D,CAAC;gBACH;gBACA,aAAa;oBACX,MAAM;gBACR;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;oBAAC;oBAAU;oBAAW;oBAAa;oBAAY;oBAAU;oBAAY;iBAAS;gBACpF,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,QAAQ;gBACV;YACF;YACA,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,WAAW;oBACX,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;YACF;YACA,QAAQ;gBACN;oBACE,MAAM,UAAU,QAAQ;oBACxB,MAAM;oBACN,UAAU;oBACV,WAAW;wBACT,OAAO;4BACL,MAAM;4BACN,GAAG;4BACH,GAAG;4BACH,IAAI;4BACJ,IAAI;4BACJ,YAAY;gCACV;oCAAE,QAAQ;oCAAG,OAAO;gCAAU;gCAC9B;oCAAE,QAAQ;oCAAK,OAAO;gCAAU;gCAChC;oCAAE,QAAQ;oCAAG,OAAO;gCAAU;6BAC/B;wBACH;wBACA,cAAc;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;oBAC5B;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;oCAC9B;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;iCAC/B;4BACH;4BACA,YAAY;4BACZ,aAAa;wBACf;oBACF;oBACA,UAAU;wBACR,MAAM;4BACJ;gCAAE,MAAM;gCAAW,MAAM;4BAAU;yBACpC;wBACD,WAAW;4BACT,OAAO;4BACP,MAAM;wBACR;oBACF;gBACF;aACD;YACD,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,cAAc;YAChB;YACA,WAAW;YACX,mBAAmB;YACnB,iBAAiB;QACnB,CAAC,GAAG;QAAC,UAAU,QAAQ;QAAE;KAAc;IAEvC,oDAAoD;IACpD,MAAM,sBAAsB,QAAQ,IAAM,CAAC;YACzC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,QAAQ,UAAU,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;oBAC7E,OAAO,CAAC;;gEAEgD,EAAE,OAAO,IAAI,CAAC;+BAC/C,EAAE,OAAO,KAAK,CAAC,aAAa,EAAE,OAAO,KAAK,CAAC;;cAE5D,EAAE,OAAO,OAAO,CAAC,YAAY,EAAE,MAAM;;;QAG3C,CAAC;gBACH;YACF;YACA,QAAQ;gBACN,QAAQ;gBACR,QAAQ;gBACR,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;oBACN,QAAQ;wBAAC;wBAAO;qBAAM;oBACtB,QAAQ;wBAAC;wBAAO;qBAAM;oBACtB,mBAAmB;oBACnB,WAAW;wBACT,cAAc;wBACd,aAAa,kBAAkB,SAAS,YAAY;wBACpD,aAAa;wBACb,YAAY;wBACZ,aAAa;oBACf;oBACA,OAAO;wBACL,MAAM;wBACN,UAAU;oBACZ;oBACA,UAAU;wBACR,OAAO;4BACL,MAAM;4BACN,UAAU;4BACV,YAAY;4BACZ,OAAO,kBAAkB,SAAS,YAAY;wBAChD;wBACA,WAAW;4BACT,YAAY;4BACZ,eAAe;4BACf,aAAa;wBACf;wBACA,OAAO;wBACP,WAAW;oBACb;oBACA,WAAW;wBACT,MAAM;oBACR;oBACA,MAAM,UAAU,YAAY;oBAC5B,eAAe;oBACf,iBAAiB;oBACjB,gBAAgB,CAAC,MAAgB,MAAM;gBACzC;aACD;QACH,CAAC,GAAG;QAAC,UAAU,YAAY;QAAE;KAAc;IAE3C,2BAA2B;IAC3B,MAAM,mBAAmB,QAAQ,IAAM,CAAC;YACtC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,OAAO,MAAM,CAAC,EAAE;oBACtB,MAAM,WAAW,KAAK,SAAS;oBAC/B,MAAM,gBAAgB,WAAW,IAAI,UAAU,SAAS,CAAC,WAAW,EAAE,GAAG,KAAK,KAAK;oBACnF,MAAM,SAAS,CAAC,CAAC,KAAK,KAAK,GAAG,aAAa,IAAI,gBAAgB,GAAG,EAAE,OAAO,CAAC;oBAC5E,OAAO,CAAC;;oEAEoD,EAAE,WAAW,EAAE;iDAClC,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;+BAChD,EAAE,OAAO,UAAU,CAAC,OAAO,YAAY,UAAU;cAClE,EAAE,OAAO,UAAU,CAAC,OAAO,KAAK,MAAM,OAAO;;;QAGnD,CAAC;gBACH;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAG,GAAG,CAAC,GAAG,IAAM,CAAC,IAAI,EAAE,IAAI,GAAG;gBACzD,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;YACF;YACA,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,WAAW;oBACX,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;YACF;YACA,QAAQ;gBACN;oBACE,MAAM,UAAU,SAAS;oBACzB,MAAM;oBACN,QAAQ;oBACR,WAAW;wBACT,OAAO;wBACP,OAAO;oBACT;oBACA,WAAW;wBACT,OAAO;oBACT;oBACA,WAAW;wBACT,OAAO;4BACL,MAAM;4BACN,GAAG;4BACH,GAAG;4BACH,IAAI;4BACJ,IAAI;4BACJ,YAAY;gCACV;oCAAE,QAAQ;oCAAG,OAAO;gCAA0B;gCAC9C;oCAAE,QAAQ;oCAAG,OAAO;gCAA2B;6BAChD;wBACH;oBACF;oBACA,UAAU;wBACR,OAAO;wBACP,WAAW;4BACT,YAAY;4BACZ,aAAa;wBACf;oBACF;gBACF;aACD;YACD,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,cAAc;YAChB;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;aACD;QACH,CAAC,GAAG;QAAC,UAAU,SAAS;QAAE;KAAc;IAExC,kDAAkD;IAClD,MAAM,WAAW,QAAQ;QACvB,MAAM,eAAe,UAAU,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;QACjE,MAAM,oBAAoB,eAAe,UAAU,SAAS,CAAC,MAAM;QACnE,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;QAC7D,MAAM,eAAe,YAAY,UAAU,QAAQ,CAAC,MAAM;QAE1D,OAAO;YACL;gBACE,OAAO;gBACP,OAAO,MAAM,aAAa,cAAc;gBACxC,MAAM,kNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,UAAU,CAAC,MAAM,EAAE,kBAAkB,cAAc,GAAG,MAAM,CAAC;gBAC7D,OAAO;YACT;YACA;gBACE,OAAO;gBACP,OAAO,MAAM,aAAa,CAAC,QAAQ;gBACnC,MAAM,wMAAA,CAAA,UAAO;gBACb,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,UAAU,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC;gBAC5C,OAAO;YACT;YACA;gBACE,OAAO;gBACP,OAAO,MAAM,UAAU,CAAC,QAAQ;gBAChC,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,UAAU;gBACV,OAAO;YACT;YACA;gBACE,OAAO;gBACP,OAAO,MAAM,MAAM,eAAe,CAAC,cAAc;gBACjD,MAAM,kNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,UAAU,CAAC,MAAM,EAAE,aAAa,cAAc,GAAG,IAAI,CAAC;gBACtD,OAAO;YACT;SACD;IACH,GAAG;QAAC;QAAW;KAAM;IAErB,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC/C;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB;QAClB,0BAA0B;QAC1B,aAAa;QACb,WAAW;YACT,aAAa;YACb,sDAAsD;YACtD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC3C,GAAG;IACL;IAEA,MAAM,cAAc;QAClB;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAKvD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,6FAA6F,EACvG,cACI,yEACA,iEACJ;;0DAEF,8OAAC;gDAAS,WAAW,CAAC,QAAQ,EAAE,cAAc,kBAAkB,IAAI;;;;;;4CAAI;;;;;;;kDAK1E,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDAAU,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;4CAAI;;;;;;;kDAKxE,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,8OAAC;gDAAO,WAAU;;;;;;4CAAY;0DAE9B,8OAAC;gDAAY,WAAW,CAAC,0CAA0C,EAAE,cAAc,eAAe,IAAI;;;;;;;;;;;;kDAIxG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,kBAAkB,CAAC;gDAClC,WAAU;;kEAEV,8OAAC;wDAAS,WAAU;;;;;;oDAAY;;;;;;;4CAIjC,gCACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,8OAAC;oEAAS,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,8OAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,8OAAC;oEAAgB,WAAU;;;;;;gEAAY;;;;;;;sEAGzC,8OAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,8OAAC;oEAAS,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,8OAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;;8EAEV,8OAAC;oEAAM,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAW1C,6BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC9D,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAKzB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DACV,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,IAAI,WAAW,EAAE;kEACtD,IAAI,MAAM;;;;;;kEAEb,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK/D,8OAAC;wCAAI,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,wDAAwD,CAAC;kDACrG,cAAA,8OAAC,IAAI,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,IAAI,KAAK,KAAK,OAAO,iBAAiB,aACvC,cAAc,CAAC;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,KAAK,OAAO,gBAAgB;;;;;;;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;uBAtCZ;;;;;;;;;;0BA6CX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;gCACC;oCAAE,IAAI;oCAAS,OAAO;oCAAiB,MAAM;gCAAU;gCACvD;oCAAE,IAAI;oCAAQ,OAAO;oCAAiB,MAAM;gCAAU;gCACtD;oCAAE,IAAI;oCAAY,OAAO;oCAAc,MAAM;gCAAS;gCACtD;oCAAE,IAAI;oCAAS,OAAO;oCAAe,MAAM,kNAAA,CAAA,aAAU;gCAAC;6BACvD,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;oCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;oCACpC,WAAW,CAAC,6FAA6F,EACvG,gBAAgB,IAAI,EAAE,GAClB,mFACA,6EACJ;;sDAEF,8OAAC,IAAI,IAAI;4CAAC,WAAU;;;;;;wCACnB,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;0CAaf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAU,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCACC,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAU,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAuD;;;;;;;;;;;;;;;;;0CAO7E,8OAAC;gCAAI,WAAU;gCAAkC,OAAO;oCAAE,SAAS,YAAY,MAAM;gCAAE;0CACrF,cAAA,8OAAC,uJAAA,CAAA,UAAY;oCACX,QAAQ;oCACR,OAAO;wCAAE,QAAQ;oCAAQ;oCACzB,MAAM;wCAAE,UAAU;oCAAM;;;;;;;;;;;;;;;;;;;;;;;YAO/B,gBAAgB,yBACf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC,uJAAA,CAAA,UAAY;gCACX,QAAQ;oCACN,GAAG,eAAe;oCAClB,SAAS;wCACP,SAAS;wCACT,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,MAAM,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG,IAAM,GAAG,EAAE,GAAG,CAAC;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;4CAC9C,UAAU;wCACZ;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,WAAW;4CACT,WAAW;4CACX,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,QAAQ;wCAAC;4CACP,MAAM,UAAU,UAAU;4CAC1B,MAAM;4CACN,WAAW;gDACT,OAAO;gDACP,cAAc;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE;4CAC5B;wCACF;qCAAE;oCACF,MAAM;wCACJ,MAAM;wCACN,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,cAAc;oCAChB;gCACF;gCACA,OAAO;oCAAE,QAAQ;gCAAQ;;;;;;;;;;;;kCAK7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC,uJAAA,CAAA,UAAY;gCACX,QAAQ;oCACN,GAAG,eAAe;oCAClB,SAAS;wCACP,SAAS;wCACT,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;wCACpD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,MAAM,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,IAAI,GAAG;wCACtD,WAAW;4CACT,OAAO,kBAAkB,SAAS,YAAY;4CAC9C,UAAU;wCACZ;oCACF;oCACA,OAAO;wCACL,MAAM;wCACN,WAAW;4CACT,WAAW;4CACX,OAAO,kBAAkB,SAAS,YAAY;wCAChD;oCACF;oCACA,QAAQ;wCAAC;4CACP,MAAM,UAAU,UAAU;4CAC1B,MAAM;4CACN,QAAQ;4CACR,WAAW;gDACT,OAAO;gDACP,OAAO;4CACT;4CACA,WAAW;gDACT,OAAO;4CACT;4CACA,WAAW;gDACT,OAAO;oDACL,MAAM;oDACN,GAAG;oDACH,GAAG;oDACH,IAAI;oDACJ,IAAI;oDACJ,YAAY;wDACV;4DAAE,QAAQ;4DAAG,OAAO;wDAA0B;wDAC9C;4DAAE,QAAQ;4DAAG,OAAO;wDAA2B;qDAChD;gDACH;4CACF;wCACF;qCAAE;oCACF,MAAM;wCACJ,MAAM;wCACN,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,cAAc;oCAChB;oCACA,UAAU;wCAAC;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;qCAAE;gCACJ;gCACA,OAAO;oCAAE,QAAQ;gCAAQ;;;;;;;;;;;;;;;;;;0BAOjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,mCAAmC,EAClD,cAAc,iBAAiB,eAC/B;;;;;;8CACF,8OAAC;oCAAK,WAAU;8CACb,cAAc,kCAAkC;;;;;;gCAElD,2BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAU,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;;gDAAK;gDAAe,YAAY,kBAAkB;;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAS,WAAU;;;;;;sDACpB,8OAAC;;gDAAK;gDAAc,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD", "debugId": null}}]}